# PandaWiki Docker 自动化部署指南

本文档提供了多种 PandaWiki 的 Docker 自动化部署方案，适用于不同的使用场景。

## 🚀 快速开始

### 方案一：一键部署脚本（推荐）

最简单的部署方式，适合快速体验和测试：

```bash
# 给脚本执行权限
chmod +x quick-deploy.sh

# 一键部署基础服务
./quick-deploy.sh
```

这将自动：
- 检查 Docker 环境
- 创建必要的数据目录
- 生成随机密码和配置
- 启动 PostgreSQL、Redis、NATS、MinIO 服务

### 方案二：完整 Docker 部署

适合生产环境的完整部署：

```bash
# 给脚本执行权限
chmod +x docker-manager.sh

# 完整部署（包含构建镜像）
./docker-manager.sh deploy
```

### 方案三：Makefile 部署

适合开发者和运维人员：

```bash
# 查看所有可用命令
make help

# 快速部署基础服务
make quick-deploy

# 完整部署
make deploy

# 查看服务状态
make status
```

## 📋 部署方案对比

| 方案 | 适用场景 | 优点 | 缺点 |
|------|----------|------|------|
| quick-deploy.sh | 快速体验、开发测试 | 简单快速、自动配置 | 仅基础服务 |
| docker-manager.sh | 生产环境 | 功能完整、易管理 | 需要构建镜像 |
| Makefile | 开发运维 | 命令简洁、功能丰富 | 需要 make 工具 |
| GitHub Actions | CI/CD | 自动化程度高 | 需要 GitHub 环境 |

## 🔧 详细配置

### 环境变量配置

主要的环境变量配置项：

```bash
# 数据库配置
POSTGRES_PASSWORD=随机生成的密码
POSTGRES_DB=panda-wiki
POSTGRES_USER=panda-wiki

# 对象存储配置
S3_ACCESS_KEY=s3panda-wiki
S3_SECRET_KEY=随机生成的密钥

# 应用配置
JWT_SECRET=随机生成的JWT密钥
ADMIN_PASSWORD=admin123
MAX_KB=50

# 端口配置
API_PORT=8000
ADMIN_PORT=5173
APP_PORT=3010
POSTGRES_PORT=5432
REDIS_PORT=6379
NATS_PORT=4222
MINIO_PORT=9000
MINIO_CONSOLE_PORT=9001
```

### 数据持久化

所有重要数据都会持久化到 `./data` 目录：

```
data/
├── postgres/     # PostgreSQL 数据
├── redis/        # Redis 数据
├── minio/        # MinIO 对象存储数据
└── nats/         # NATS 消息队列数据
```

### 服务架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Admin Panel   │    │   User App      │    │   API Server    │
│   (Port 5173)   │    │   (Port 3010)   │    │   (Port 8000)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │     Redis       │    │     MinIO       │
│   (Port 5432)   │    │   (Port 6379)   │    │   (Port 9000)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                 │
                       ┌─────────────────┐
                       │      NATS       │
                       │   (Port 4222)   │
                       └─────────────────┘
```

## 🛠️ 常用操作

### 使用 docker-manager.sh

```bash
# 部署服务
./docker-manager.sh deploy

# 查看状态
./docker-manager.sh status

# 查看日志
./docker-manager.sh logs

# 查看特定服务日志
./docker-manager.sh logs panda-wiki-api

# 备份数据
./docker-manager.sh backup

# 更新服务
./docker-manager.sh update

# 停止服务
./docker-manager.sh stop

# 重启服务
./docker-manager.sh restart

# 清理所有数据（危险操作）
./docker-manager.sh cleanup
```

### 使用 Makefile

```bash
# 查看帮助
make help

# 快速部署基础服务
make quick-deploy

# 完整部署
make deploy

# 构建所有镜像
make build-all

# 查看服务状态
make status

# 查看日志
make logs
make logs-api
make logs-admin
make logs-app

# 备份数据
make backup

# 健康检查
make health

# 显示配置
make show-config
```

## 🔍 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :8000
   
   # 修改 .env 文件中的端口配置
   API_PORT=8001
   ```

2. **权限问题**
   ```bash
   # 确保数据目录权限正确
   sudo chown -R $USER:$USER ./data
   chmod -R 755 ./data
   ```

3. **内存不足**
   ```bash
   # 检查系统资源
   docker system df
   docker system prune -a
   ```

4. **服务启动失败**
   ```bash
   # 查看详细日志
   docker-compose logs -f service-name
   
   # 检查服务健康状态
   docker-compose ps
   ```

### 日志查看

```bash
# 查看所有服务日志
docker-compose -f docker-compose.prod.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.prod.yml logs -f panda-wiki-api

# 查看最近的日志
docker-compose -f docker-compose.prod.yml logs --tail=100 -f
```

## 🔒 安全建议

1. **修改默认密码**
   ```bash
   # 修改 .env 文件中的密码
   ADMIN_PASSWORD=your-secure-password
   POSTGRES_PASSWORD=your-secure-db-password
   ```

2. **使用 HTTPS**
   - 配置反向代理（Nginx/Caddy）
   - 申请 SSL 证书

3. **网络安全**
   - 使用防火墙限制端口访问
   - 配置 Docker 网络隔离

4. **定期备份**
   ```bash
   # 设置定时备份
   crontab -e
   0 2 * * * /path/to/docker-manager.sh backup
   ```

## 📈 监控和维护

### 健康检查

```bash
# API 健康检查
curl -f http://localhost:8000/health

# 数据库连接检查
docker-compose exec panda-wiki-postgres pg_isready -U panda-wiki

# Redis 连接检查
docker-compose exec panda-wiki-redis redis-cli ping
```

### 性能监控

```bash
# 查看容器资源使用
docker stats

# 查看磁盘使用
du -sh ./data/*

# 查看日志大小
docker system df
```

## 🚀 生产环境部署

### 推荐配置

1. **硬件要求**
   - CPU: 4核心以上
   - 内存: 8GB 以上
   - 磁盘: 100GB 以上 SSD

2. **系统配置**
   ```bash
   # 增加文件描述符限制
   echo "* soft nofile 65536" >> /etc/security/limits.conf
   echo "* hard nofile 65536" >> /etc/security/limits.conf
   
   # 优化内核参数
   echo "vm.max_map_count=262144" >> /etc/sysctl.conf
   sysctl -p
   ```

3. **Docker 配置**
   ```json
   {
     "log-driver": "json-file",
     "log-opts": {
       "max-size": "10m",
       "max-file": "3"
     },
     "storage-driver": "overlay2"
   }
   ```

### 高可用部署

对于生产环境，建议使用 Docker Swarm 或 Kubernetes 进行高可用部署。

## 📞 支持

如果遇到问题，请：

1. 查看日志文件
2. 检查 GitHub Issues
3. 提交新的 Issue
4. 联系技术支持

---

**注意**: 请根据实际需求选择合适的部署方案，并在生产环境中进行充分测试。
