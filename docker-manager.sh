#!/bin/bash

# PandaWiki Docker 自动化部署脚本
# 支持一键部署、更新、停止、重启等操作

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 设置错误处理
set -e
trap 'handle_error $? $LINENO' ERR

# 错误处理函数
handle_error() {
    local exit_code=$1
    local line_number=$2
    echo -e "${RED}Error occurred in script at line $line_number with exit code $exit_code${NC}"
    exit $exit_code
}

# 项目配置
PROJECT_NAME="panda-wiki"
COMPOSE_FILE="docker-compose.prod.yml"
ENV_FILE=".env.prod"
DATA_DIR="./data"
BACKUP_DIR="./backups"

# 检查依赖
check_dependencies() {
    echo -e "${BLUE}Checking dependencies...${NC}"
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}Docker is not installed. Please install Docker first.${NC}"
        exit 1
    fi
    
    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        echo -e "${RED}Docker Compose is not installed. Please install Docker Compose first.${NC}"
        exit 1
    fi

    # 检查 pnpm
    if ! command -v pnpm &> /dev/null; then
        echo -e "${RED}pnpm is not installed. Please install pnpm first: npm install -g pnpm${NC}"
        exit 1
    fi

    echo -e "${GREEN}Dependencies check passed.${NC}"
}

# 创建必要的目录
create_directories() {
    echo -e "${BLUE}Creating necessary directories...${NC}"
    mkdir -p "$DATA_DIR"
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$DATA_DIR/postgres"
    mkdir -p "$DATA_DIR/redis"
    mkdir -p "$DATA_DIR/minio"
    mkdir -p "$DATA_DIR/nats"
    echo -e "${GREEN}Directories created.${NC}"
}

# 生成环境配置文件
generate_env_file() {
    echo -e "${BLUE}Generating environment configuration...${NC}"
    
    if [[ ! -f "$ENV_FILE" ]]; then
        cat > "$ENV_FILE" << EOF
# PandaWiki Production Environment Configuration

# 基础配置
COMPOSE_PROJECT_NAME=$PROJECT_NAME
TZ=Asia/Shanghai

# 数据库配置
POSTGRES_DB=panda-wiki
POSTGRES_USER=panda-wiki
POSTGRES_PASSWORD=$(openssl rand -base64 32)
PG_DSN=host=panda-wiki-postgres user=panda-wiki password=\${POSTGRES_PASSWORD} dbname=panda-wiki port=5432 sslmode=disable TimeZone=Asia/Shanghai

# Redis配置
REDIS_ADDR=panda-wiki-redis:6379

# NATS配置
MQ_NATS_SERVER=nats://panda-wiki-nats:4222

# MinIO配置
S3_ENDPOINT=panda-wiki-minio:9000
S3_ACCESS_KEY=s3panda-wiki
S3_SECRET_KEY=$(openssl rand -base64 32)

# 应用配置
JWT_SECRET=$(openssl rand -base64 64)
ADMIN_PASSWORD=admin123
DATA_DIR=/app/data

# 端口配置
API_PORT=8000
ADMIN_PORT=5173
APP_PORT=3010
POSTGRES_PORT=5432
REDIS_PORT=6379
NATS_PORT=4222
MINIO_PORT=9000
MINIO_CONSOLE_PORT=9001

# 其他配置
CADDY_API=disabled
MAX_KB=50
CRAWLER_SERVICE_URL=disabled
RAG_CT_RAG_BASE_URL=http://localhost:8080/api/v1
EOF
        echo -e "${GREEN}Environment file created: $ENV_FILE${NC}"
        echo -e "${YELLOW}Please review and modify the configuration if needed.${NC}"
    else
        echo -e "${YELLOW}Environment file already exists: $ENV_FILE${NC}"
    fi
}

# 生成生产环境 docker-compose 文件
generate_compose_file() {
    echo -e "${BLUE}Generating production docker-compose file...${NC}"
    
    cat > "$COMPOSE_FILE" << 'EOF'
version: '3.8'

services:
  # PostgreSQL数据库
  panda-wiki-postgres:
    image: postgres:15-alpine
    container_name: ${COMPOSE_PROJECT_NAME}-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      TZ: ${TZ}
    ports:
      - "${POSTGRES_PORT}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - panda-wiki-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存
  panda-wiki-redis:
    image: redis:7-alpine
    container_name: ${COMPOSE_PROJECT_NAME}-redis
    ports:
      - "${REDIS_PORT}:6379"
    volumes:
      - redis_data:/data
    networks:
      - panda-wiki-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # NATS消息队列
  panda-wiki-nats:
    image: nats:2.10-alpine
    container_name: ${COMPOSE_PROJECT_NAME}-nats
    ports:
      - "${NATS_PORT}:4222"
      - "8222:8222"
    command: ["--jetstream", "--store_dir", "/data"]
    volumes:
      - nats_data:/data
    networks:
      - panda-wiki-network
    restart: unless-stopped

  # MinIO对象存储
  panda-wiki-minio:
    image: minio/minio:latest
    container_name: ${COMPOSE_PROJECT_NAME}-minio
    environment:
      MINIO_ROOT_USER: ${S3_ACCESS_KEY}
      MINIO_ROOT_PASSWORD: ${S3_SECRET_KEY}
    ports:
      - "${MINIO_PORT}:9000"
      - "${MINIO_CONSOLE_PORT}:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - panda-wiki-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PandaWiki API服务
  panda-wiki-api:
    image: pandawiki/api:latest
    container_name: ${COMPOSE_PROJECT_NAME}-api
    environment:
      - PG_DSN=${PG_DSN}
      - REDIS_ADDR=${REDIS_ADDR}
      - MQ_NATS_SERVER=${MQ_NATS_SERVER}
      - S3_ENDPOINT=${S3_ENDPOINT}
      - S3_ACCESS_KEY=${S3_ACCESS_KEY}
      - S3_SECRET_KEY=${S3_SECRET_KEY}
      - JWT_SECRET=${JWT_SECRET}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD}
      - DATA_DIR=${DATA_DIR}
      - CADDY_API=${CADDY_API}
      - MAX_KB=${MAX_KB}
      - CRAWLER_SERVICE_URL=${CRAWLER_SERVICE_URL}
      - RAG_CT_RAG_BASE_URL=${RAG_CT_RAG_BASE_URL}
      - TZ=${TZ}
    ports:
      - "${API_PORT}:8000"
    volumes:
      - api_data:/app/data
    networks:
      - panda-wiki-network
    depends_on:
      panda-wiki-postgres:
        condition: service_healthy
      panda-wiki-redis:
        condition: service_healthy
      panda-wiki-minio:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PandaWiki 管理后台
  panda-wiki-admin:
    image: pandawiki/admin:latest
    container_name: ${COMPOSE_PROJECT_NAME}-admin
    ports:
      - "${ADMIN_PORT}:80"
    networks:
      - panda-wiki-network
    depends_on:
      - panda-wiki-api
    restart: unless-stopped

  # PandaWiki 用户前台
  panda-wiki-app:
    image: pandawiki/app:latest
    container_name: ${COMPOSE_PROJECT_NAME}-app
    environment:
      - API_URL=http://panda-wiki-api:8000
      - TZ=${TZ}
    ports:
      - "${APP_PORT}:3010"
    networks:
      - panda-wiki-network
    depends_on:
      - panda-wiki-api
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  nats_data:
  minio_data:
  api_data:

networks:
  panda-wiki-network:
    driver: bridge
EOF

    echo -e "${GREEN}Production docker-compose file created: $COMPOSE_FILE${NC}"
}

# 构建镜像
build_images() {
    echo -e "${BLUE}Building Docker images...${NC}"
    
    # 构建后端API镜像
    echo -e "${YELLOW}Building API image...${NC}"
    docker build -f backend/Dockerfile.api -t pandawiki/api:latest ./backend
    
    # 构建前端管理后台镜像
    echo -e "${YELLOW}Building admin frontend...${NC}"
    cd web/admin
    pnpm install
    pnpm run build
    docker build -t pandawiki/admin:latest .
    cd ../..

    # 构建用户前台镜像
    echo -e "${YELLOW}Building app frontend...${NC}"
    cd web/app
    pnpm install
    pnpm run build
    docker build -t pandawiki/app:latest .
    cd ../..
    
    echo -e "${GREEN}All images built successfully.${NC}"
}

# 部署服务
deploy() {
    echo -e "${BLUE}Deploying PandaWiki...${NC}"
    
    check_dependencies
    create_directories
    generate_env_file
    generate_compose_file
    
    # 询问是否构建镜像
    read -p "Do you want to build images locally? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        build_images
    fi
    
    # 启动服务
    echo -e "${YELLOW}Starting services...${NC}"
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d
    
    echo -e "${GREEN}PandaWiki deployed successfully!${NC}"
    show_status
}

# 显示服务状态
show_status() {
    echo -e "${BLUE}Service Status:${NC}"
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps
    
    echo -e "\n${BLUE}Access URLs:${NC}"
    echo -e "Admin Panel: ${GREEN}http://localhost:$(grep ADMIN_PORT $ENV_FILE | cut -d'=' -f2)${NC}"
    echo -e "User App: ${GREEN}http://localhost:$(grep APP_PORT $ENV_FILE | cut -d'=' -f2)${NC}"
    echo -e "API: ${GREEN}http://localhost:$(grep API_PORT $ENV_FILE | cut -d'=' -f2)${NC}"
    echo -e "MinIO Console: ${GREEN}http://localhost:$(grep MINIO_CONSOLE_PORT $ENV_FILE | cut -d'=' -f2)${NC}"
}

# 停止服务
stop() {
    echo -e "${YELLOW}Stopping PandaWiki services...${NC}"
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" down
    echo -e "${GREEN}Services stopped.${NC}"
}

# 重启服务
restart() {
    echo -e "${YELLOW}Restarting PandaWiki services...${NC}"
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" restart
    echo -e "${GREEN}Services restarted.${NC}"
    show_status
}

# 更新服务
update() {
    echo -e "${BLUE}Updating PandaWiki...${NC}"
    
    # 拉取最新镜像
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" pull
    
    # 重新启动服务
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d
    
    echo -e "${GREEN}Update completed.${NC}"
    show_status
}

# 备份数据
backup() {
    echo -e "${BLUE}Creating backup...${NC}"
    
    BACKUP_NAME="panda-wiki-backup-$(date +%Y%m%d-%H%M%S)"
    BACKUP_PATH="$BACKUP_DIR/$BACKUP_NAME"
    
    mkdir -p "$BACKUP_PATH"
    
    # 备份数据库
    echo -e "${YELLOW}Backing up database...${NC}"
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T panda-wiki-postgres pg_dump -U panda-wiki panda-wiki > "$BACKUP_PATH/database.sql"
    
    # 备份MinIO数据
    echo -e "${YELLOW}Backing up MinIO data...${NC}"
    docker cp "${PROJECT_NAME}-minio:/data" "$BACKUP_PATH/minio-data"
    
    # 备份配置文件
    cp "$ENV_FILE" "$BACKUP_PATH/"
    cp "$COMPOSE_FILE" "$BACKUP_PATH/"
    
    echo -e "${GREEN}Backup created: $BACKUP_PATH${NC}"
}

# 查看日志
logs() {
    local service=$1
    if [[ -z "$service" ]]; then
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs -f
    else
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs -f "$service"
    fi
}

# 清理
cleanup() {
    echo -e "${YELLOW}Cleaning up...${NC}"
    
    read -p "This will remove all containers, volumes, and images. Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" down -v --rmi all
        echo -e "${GREEN}Cleanup completed.${NC}"
    else
        echo -e "${YELLOW}Cleanup cancelled.${NC}"
    fi
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}PandaWiki Docker Manager${NC}"
    echo -e "Usage: $0 [COMMAND]"
    echo
    echo -e "${YELLOW}Commands:${NC}"
    echo -e "  deploy    Deploy PandaWiki (first time setup)"
    echo -e "  start     Start all services"
    echo -e "  stop      Stop all services"
    echo -e "  restart   Restart all services"
    echo -e "  status    Show service status"
    echo -e "  update    Update to latest version"
    echo -e "  backup    Create data backup"
    echo -e "  logs      Show logs (optional: specify service name)"
    echo -e "  cleanup   Remove all containers and data"
    echo -e "  help      Show this help message"
    echo
    echo -e "${YELLOW}Examples:${NC}"
    echo -e "  $0 deploy"
    echo -e "  $0 logs panda-wiki-api"
    echo -e "  $0 backup"
}

# 主函数
main() {
    case "${1:-help}" in
        deploy)
            deploy
            ;;
        start)
            docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d
            show_status
            ;;
        stop)
            stop
            ;;
        restart)
            restart
            ;;
        status)
            show_status
            ;;
        update)
            update
            ;;
        backup)
            backup
            ;;
        logs)
            logs "$2"
            ;;
        cleanup)
            cleanup
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            echo -e "${RED}Unknown command: $1${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
