version: '3.8'

services:
  # PostgreSQL数据库
  panda-wiki-postgres:
    image: postgres:15-alpine
    container_name: panda-wiki-postgres
    environment:
      POSTGRES_DB: panda-wiki
      POSTGRES_USER: panda-wiki
      POSTGRES_PASSWORD: panda-wiki-secret
      TZ: Asia/Shanghai
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - panda-wiki-network

  # Redis缓存
  panda-wiki-redis:
    image: redis:7-alpine
    container_name: panda-wiki-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - panda-wiki-network

  # NATS消息队列
  panda-wiki-nats:
    image: nats:2.10-alpine
    container_name: panda-wiki-nats
    ports:
      - "4222:4222"
      - "8222:8222"
    command: ["--jetstream", "--store_dir", "/data"]
    volumes:
      - nats_data:/data
    networks:
      panda-wiki-network:
        ipv4_address: *************

  # MinIO对象存储
  panda-wiki-minio:
    image: minio/minio:latest
    container_name: panda-wiki-minio
    environment:
      MINIO_ROOT_USER: s3panda-wiki
      MINIO_ROOT_PASSWORD: s3panda-wiki-secret
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - panda-wiki-network

  # 临时API模拟服务 (用于演示)
  panda-wiki-api-mock:
    image: nginx:alpine
    container_name: panda-wiki-api-mock
    ports:
      - "8000:80"
    volumes:
      - ./mock-api:/usr/share/nginx/html
    networks:
      - panda-wiki-network

volumes:
  postgres_data:
  redis_data:
  nats_data:
  minio_data:

networks:
  panda-wiki-network:
    driver: bridge
    ipam:
      config:
        - subnet: ************/24
