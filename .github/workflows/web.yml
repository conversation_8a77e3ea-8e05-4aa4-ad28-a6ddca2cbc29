name: Web Build and Push

on:
  push:
    branches:
      - frontend-*
      - admin-*
      - app-*
    tags:
      - "admin-v[0-9]+.[0-9]+.[0-9]+*"
      - "app-v[0-9]+.[0-9]+.[0-9]+*"
      - "v[0-9]+.[0-9]+.[0-9]+*"
  pull_request:
    branches:
      - main
    paths:
      - 'web/**'

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        project: [admin, app]

    outputs:
      version: ${{ steps.get_version.outputs.VERSION }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 10

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v3
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ matrix.project }}-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-${{ matrix.project }}-

      - name: Get version
        id: get_version
        run: |
          if [[ $GITHUB_REF == refs/tags/* ]]; then
            if [[ $GITHUB_REF == refs/tags/${{ matrix.project }}-v* ]]; then
              echo "VERSION=${GITHUB_REF#refs/tags/${{ matrix.project }}-v}" >> $GITHUB_OUTPUT
            else
              echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT
            fi
          else
            echo "VERSION=${GITHUB_SHA::7}" >> $GITHUB_OUTPUT
          fi

      - name: Install dependencies
        run: |
          cd web/${{ matrix.project }}
          pnpm install

      - name: Build
        run: |
          cd web/${{ matrix.project }}
          if [ "${{ matrix.project }}" == "admin" ]; then
            echo "VITE_APP_VERSION=${{ steps.get_version.outputs.VERSION }}" >> .env.production
          fi
          pnpm run build

      - name: 'Tar files'
        run: tar -cvf web/${{ matrix.project }}/dist.tar web/${{ matrix.project }}/dist

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.project }}-build
          path: web/${{ matrix.project }}/dist.tar
          if-no-files-found: error
          include-hidden-files: true

  package:
    needs: build
    runs-on: ubuntu-latest
    strategy:
      matrix:
        project: [admin, app]
    if: startsWith(github.ref, 'refs/tags/')

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: ${{ matrix.project }}-build

      - name: Extract files
        run: |
          tar -xvf dist.tar

      - name: Check file structure
        run: |
          echo "Current directory: $(pwd)"
          echo "Listing web/${{ matrix.project }} directory:"
          ls -la web/${{ matrix.project }}
          echo "Listing web/${{ matrix.project }}/dist directory:"
          ls -la web/${{ matrix.project }}/dist

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Aliyun Container Registry
        uses: docker/login-action@v3
        with:
          registry: chaitin-registry.cn-hangzhou.cr.aliyuncs.com
          username: ${{ secrets.CT_ALIYUN_USER }}
          password: ${{ secrets.CT_ALIYUN_PASS }}

      - name: Package and push
        uses: docker/build-push-action@v5
        with:
          context: ./web/${{ matrix.project }}
          file: ./web/${{ matrix.project }}/Dockerfile
          push: true
          platforms: linux/amd64, linux/arm64
          tags: chaitin-registry.cn-hangzhou.cr.aliyuncs.com/chaitin/panda-wiki-${{ matrix.project == 'admin' && 'nginx' || 'app' }}:v${{ needs.build.outputs.version }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
