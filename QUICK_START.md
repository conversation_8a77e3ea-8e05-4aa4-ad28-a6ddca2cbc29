# PandaWiki 快速启动指南

## 🚀 一键启动（推荐）

### 前提条件
- 安装 Docker Desktop 并确保运行中
- 确保端口 3010、5173、8000、9001 未被占用
- 确保有稳定的网络连接（首次启动需要下载依赖）

### 启动步骤

1. **克隆或下载项目代码**
2. **进入项目目录**
   ```bash
   cd PandaWiki
   ```
3. **运行启动脚本**
   ```bash
   ./start-pandawiki.sh
   ```

**首次启动说明**：
- 脚本会自动检查所需的 Docker 镜像
- 如果镜像不存在，会自动构建（需要 5-10 分钟）
- 后续启动会直接使用已构建的镜像，速度很快

就这么简单！🎉

## 🔧 手动启动

如果你更喜欢手动控制，可以使用以下命令：

```bash
# 启动服务（首次运行会自动构建镜像）
docker-compose -f docker-compose.prod.yml up -d --build

# 查看服务状态
docker-compose -f docker-compose.prod.yml ps

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f

# 停止服务
docker-compose -f docker-compose.prod.yml down
```

## 📱 访问地址

启动成功后，你可以通过以下地址访问：

- **用户前台**：http://localhost:3010
- **管理后台**：http://localhost:5173
- **API 接口**：http://localhost:8000
- **MinIO 控制台**：http://localhost:9001

## 🔑 默认账号

- **管理后台密码**：admin123
- **MinIO 账号**：pandawiki / (自动生成的密码)

## 🛠️ 常见问题

### 1. 端口冲突
如果遇到端口冲突，可以修改 `docker-compose.prod.yml` 中的端口映射：
```yaml
ports:
  - "3011:3010"  # 将 3010 改为 3011
```

### 2. 构建失败
如果构建失败，尝试清理 Docker 缓存：
```bash
docker system prune -a
```

### 3. 服务无法启动
检查 Docker Desktop 是否正常运行，并确保有足够的磁盘空间。

## 📊 系统要求

- **内存**：至少 4GB RAM
- **磁盘**：至少 10GB 可用空间
- **Docker**：Docker Desktop 4.0+
- **操作系统**：Windows 10+、macOS 10.15+、Linux

## 🔄 更新

要更新到最新版本：
```bash
git pull
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml up -d --build
```

## 📝 数据备份

重要数据存储在 `data/` 目录中，建议定期备份：
```bash
tar -czf pandawiki-backup-$(date +%Y%m%d).tar.gz data/
```
