package usecase

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"os"
	"strings"

	"github.com/chaitin/panda-wiki/domain"
	"github.com/chaitin/panda-wiki/log"
)

type CrawlerUsecase struct {
	client *http.Client
	logger *log.Logger
}

func NewCrawlerUsecase(logger *log.Logger) (*CrawlerUsecase, error) {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true,
		},
	}
	return &CrawlerUsecase{
		client: &http.Client{
			Transport: transport,
		},
		logger: logger,
	}, nil
}

func (u *CrawlerUsecase) ScrapeURL(ctx context.Context, targetURL string, kbID string) (*domain.ScrapeResp, error) {
	// 支持通过环境变量配置crawler服务地址
	crawleServiceURL := os.Getenv("CRAWLER_SERVICE_URL")
	if crawleServiceURL == "" {
		crawleServiceURL = "http://panda-wiki-crawler:8080/api/v1/scrape"
	}

	// 如果设置为disabled，返回模拟的成功响应
	if crawleServiceURL == "disabled" {
		return &domain.ScrapeResp{
			Title:   "文档已上传",
			Content: "文档内容已成功上传到知识库，但URL抓取功能当前已禁用。",
		}, nil
	}

	// for uploaded file key
	if strings.HasPrefix(targetURL, "/static-file") {
		targetURL = "https://panda-wiki-nginx:8080" + targetURL
	}

	reqBody := domain.ScrapeRequest{
		URL:  targetURL,
		KbID: kbID,
	}
	body, err := json.Marshal(reqBody)
	if err != nil {
		return nil, err
	}
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, crawleServiceURL, bytes.NewBuffer(body))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	resp, err := u.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	u.logger.Debug("scrape url", "url", targetURL, "resp", string(respBody))
	var scrapeResp domain.ScrapeResponse
	err = json.Unmarshal(respBody, &scrapeResp)
	if err != nil {
		return nil, err
	}
	if scrapeResp.Err != 0 {
		return nil, errors.New(scrapeResp.Msg)
	}
	return &domain.ScrapeResp{
		Title:   scrapeResp.Data.Title,
		Content: scrapeResp.Data.Markdown,
	}, nil
}
