FROM --platform=$BUILDPLATFORM dockerproxy.com/library/golang:1.23-alpine AS builder

WORKDIR /src
ENV CGO_ENABLED=0
ENV GOTOOLCHAIN=go1.24.3
ENV GOPROXY=https://goproxy.cn,direct

COPY go.mod go.sum ./
RUN --mount=type=cache,target=/go/pkg/mod \
    go mod download

COPY . .
ARG TARGETOS TARGETARCH
RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    GOOS=$TARGETOS GOARCH=$TARGETARCH go build \
    -ldflags="-w -s" -o api ./cmd/api

FROM dockerproxy.com/library/alpine:3.20 AS api

# 使用国内镜像源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories \
    && apk update \
    && apk upgrade \
    && apk add --no-cache ca-certificates tzdata wget \
    && update-ca-certificates 2>/dev/null || true \
    && rm -rf /var/cache/apk/*

WORKDIR /app
COPY --from=builder /src/api /app/
COPY --from=builder /src/web /app/web

RUN addgroup -g 1001 -S pandawiki && \
    adduser -S pandawiki -u 1001 -G pandawiki && \
    mkdir -p /app/data && \
    chown -R pandawiki:pandawiki /app

USER pandawiki
EXPOSE 8000
CMD ["./api"]
