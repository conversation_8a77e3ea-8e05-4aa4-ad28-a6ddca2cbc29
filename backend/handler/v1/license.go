package v1

import (
	"time"

	"github.com/labstack/echo/v4"

	"github.com/chaitin/panda-wiki/handler"
)

type LicenseHandler struct {
	*handler.BaseHandler
}

type LicenseInfo struct {
	Edition   int   `json:"edition"`    // 0: 社区版, 1: 专业版, 2: 企业版
	ExpiredAt int64 `json:"expired_at"` // 过期时间戳
	StartedAt int64 `json:"started_at"` // 开始时间戳
}

func NewLicenseHandler(e *echo.Echo, baseHandler *handler.BaseHandler) *LicenseHandler {
	h := &LicenseHandler{
		BaseHandler: baseHandler,
	}

	group := e.Group("/api/v1/license")
	group.GET("", h.GetLicenseInfo)
	group.POST("", h.ActiveLicense)

	return h
}

// GetLicenseInfo
//
//	@Summary		GetLicenseInfo
//	@Description	获取许可证信息
//	@Tags			license
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	domain.Response{data=LicenseInfo}
//	@Router			/api/v1/license [get]
func (h *LicenseHandler) GetLicenseInfo(c echo.Context) error {
	// 返回默认的企业版许可证信息（开发环境）
	now := time.Now()
	licenseInfo := LicenseInfo{
		Edition:   2, // 企业版 (0: 社区版, 1: 专业版, 2: 企业版)
		StartedAt: now.Unix(),
		ExpiredAt: now.AddDate(10, 0, 0).Unix(), // 10年后过期
	}

	return h.NewResponseWithData(c, licenseInfo)
}

// ActiveLicense
//
//	@Summary		ActiveLicense
//	@Description	激活许可证
//	@Tags			license
//	@Accept			multipart/form-data
//	@Produce		json
//	@Param			file	formData	file	true	"许可证文件"
//	@Success		200		{object}	domain.Response
//	@Router			/api/v1/license [post]
func (h *LicenseHandler) ActiveLicense(c echo.Context) error {
	// 在开发环境中，直接返回成功
	return h.NewResponseWithData(c, map[string]string{
		"message": "许可证激活成功（开发环境模拟）",
	})
}
