package v1

import (
	"fmt"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"

	"github.com/chaitin/panda-wiki/config"
	"github.com/chaitin/panda-wiki/domain"
	"github.com/chaitin/panda-wiki/handler"
	"github.com/chaitin/panda-wiki/log"
	"github.com/chaitin/panda-wiki/middleware"
	"github.com/chaitin/panda-wiki/store/s3"
	"github.com/chaitin/panda-wiki/usecase"
)

type FileHandler struct {
	*handler.BaseHandler
	logger      *log.Logger
	auth        middleware.AuthMiddleware
	config      *config.Config
	fileUsecase *usecase.FileUsecase
}

func NewFileHandler(echo *echo.Echo, baseHandler *handler.BaseHandler, logger *log.Logger, auth middleware.AuthMiddleware, minioClient *s3.MinioClient, config *config.Config, fileUsecase *usecase.FileUsecase) *FileHandler {
	h := &FileHandler{
		BaseHandler: baseHandler,
		logger:      logger.WithModule("handler.v1.file"),
		auth:        auth,
		config:      config,
		fileUsecase: fileUsecase,
	}
	group := echo.Group("/api/v1/file", h.auth.Authorize)
	group.POST("/upload", h.Upload)

	// 添加静态文件服务路由
	staticGroup := echo.Group("/static-file")
	staticGroup.GET("/*", h.ServeStaticFile)

	return h
}

// Upload
//
//	@Summary		Upload File
//	@Description	Upload File
//	@Tags			file
//	@Accept			multipart/form-data
//	@Param			file	formData	file	true	"File"
//	@Param			kb_id	formData	string	false	"Knowledge Base ID"
//	@Success		200		{object}	domain.ObjectUploadResp
//	@Router			/api/v1/file/upload [post]
func (h *FileHandler) Upload(c echo.Context) error {
	cxt := c.Request().Context()
	kbID := c.FormValue("kb_id")
	if kbID == "" {
		kbID = uuid.New().String()
	}
	file, err := c.FormFile("file")
	if err != nil {
		return h.NewResponseWithError(c, "failed to get file", err)
	}

	key, err := h.fileUsecase.UploadFile(cxt, kbID, file)
	if err != nil {
		return h.NewResponseWithError(c, "upload failed", err)
	}

	return h.NewResponseWithData(c, domain.ObjectUploadResp{
		Key: "/static-file/" + key,
	})
}

// ServeStaticFile
//
//	@Summary		Serve Static File
//	@Description	Serve static files from MinIO S3
//	@Tags			file
//	@Param			path	path	string	true	"File path"
//	@Success		200		{file}	file
//	@Router			/static-file/{path} [get]
func (h *FileHandler) ServeStaticFile(c echo.Context) error {
	// 获取文件路径，去掉 /static-file/ 前缀
	filePath := c.Param("*")
	if filePath == "" {
		return echo.NewHTTPError(404, "file not found")
	}

	// 从MinIO获取文件
	object, err := h.fileUsecase.GetFile(c.Request().Context(), filePath)
	if err != nil {
		h.logger.Error("failed to get file from s3", "path", filePath, "error", err)
		return echo.NewHTTPError(404, "file not found")
	}
	defer object.Close()

	// 获取文件信息
	stat, err := object.Stat()
	if err != nil {
		h.logger.Error("failed to get file stat", "path", filePath, "error", err)
		return echo.NewHTTPError(404, "file not found")
	}

	// 设置响应头
	c.Response().Header().Set("Content-Type", stat.ContentType)
	c.Response().Header().Set("Content-Length", fmt.Sprintf("%d", stat.Size))

	// 流式传输文件内容
	return c.Stream(200, stat.ContentType, object)
}
