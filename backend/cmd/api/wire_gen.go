// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/chaitin/panda-wiki/config"
	"github.com/chaitin/panda-wiki/handler"
	"github.com/chaitin/panda-wiki/handler/share"
	"github.com/chaitin/panda-wiki/handler/v1"
	"github.com/chaitin/panda-wiki/log"
	"github.com/chaitin/panda-wiki/middleware"
	"github.com/chaitin/panda-wiki/mq"
	cache2 "github.com/chaitin/panda-wiki/repo/cache"
	ipdb2 "github.com/chaitin/panda-wiki/repo/ipdb"
	mq2 "github.com/chaitin/panda-wiki/repo/mq"
	pg2 "github.com/chaitin/panda-wiki/repo/pg"
	"github.com/chaitin/panda-wiki/server/http"
	"github.com/chaitin/panda-wiki/store/cache"
	"github.com/chaitin/panda-wiki/store/ipdb"
	"github.com/chaitin/panda-wiki/store/pg"
	"github.com/chaitin/panda-wiki/store/rag"
	"github.com/chaitin/panda-wiki/store/s3"
	"github.com/chaitin/panda-wiki/telemetry"
	"github.com/chaitin/panda-wiki/usecase"
)

// Injectors from wire.go:

func createApp() (*App, error) {
	configConfig, err := config.NewConfig()
	if err != nil {
		return nil, err
	}
	logger := log.NewLogger(configConfig)
	readOnlyMiddleware := middleware.NewReadonlyMiddleware(logger)
	echo := http.NewEcho(logger, configConfig, readOnlyMiddleware)
	httpServer := &http.HTTPServer{
		Echo: echo,
	}
	db, err := pg.NewDB(configConfig)
	if err != nil {
		return nil, err
	}
	userAccessRepository := pg2.NewUserAccessRepository(db, logger)
	authMiddleware, err := middleware.NewAuthMiddleware(configConfig, logger, userAccessRepository)
	if err != nil {
		return nil, err
	}
	ragService, err := rag.NewRAGService(configConfig, logger)
	if err != nil {
		return nil, err
	}
	knowledgeBaseRepository := pg2.NewKnowledgeBaseRepository(db, configConfig, logger, ragService)
	nodeRepository := pg2.NewNodeRepository(db, logger)
	mqProducer, err := mq.NewMQProducer(configConfig, logger)
	if err != nil {
		return nil, err
	}
	ragRepository := mq2.NewRAGRepository(mqProducer)
	cacheCache, err := cache.NewCache(configConfig)
	if err != nil {
		return nil, err
	}
	kbRepo := cache2.NewKBRepo(cacheCache)
	knowledgeBaseUsecase, err := usecase.NewKnowledgeBaseUsecase(knowledgeBaseRepository, nodeRepository, ragRepository, ragService, kbRepo, logger, configConfig)
	if err != nil {
		return nil, err
	}
	shareAuthMiddleware := middleware.NewShareAuthMiddleware(logger, knowledgeBaseUsecase)
	baseHandler := handler.NewBaseHandler(echo, logger, configConfig, authMiddleware, shareAuthMiddleware)
	userRepository := pg2.NewUserRepository(db, logger)
	userUsecase, err := usecase.NewUserUsecase(userRepository, logger, configConfig)
	if err != nil {
		return nil, err
	}
	userHandler := v1.NewUserHandler(echo, baseHandler, logger, userUsecase, authMiddleware, configConfig)
	conversationRepository := pg2.NewConversationRepository(db, logger)
	modelRepository := pg2.NewModelRepository(db, logger)
	llmUsecase := usecase.NewLLMUsecase(configConfig, ragService, conversationRepository, knowledgeBaseRepository, nodeRepository, modelRepository, logger)
	knowledgeBaseHandler := v1.NewKnowledgeBaseHandler(baseHandler, echo, knowledgeBaseUsecase, llmUsecase, authMiddleware, logger)
	minioClient, err := s3.NewMinioClient(configConfig)
	if err != nil {
		return nil, err
	}
	nodeUsecase := usecase.NewNodeUsecase(nodeRepository, ragRepository, knowledgeBaseRepository, llmUsecase, logger, minioClient, modelRepository)
	nodeHandler := v1.NewNodeHandler(baseHandler, echo, nodeUsecase, authMiddleware, logger)
	appRepository := pg2.NewAppRepository(db, logger)
	geoRepo := cache2.NewGeoCache(cacheCache, logger)
	ipdbIPDB, err := ipdb.NewIPDB(configConfig, logger)
	if err != nil {
		return nil, err
	}
	ipAddressRepo := ipdb2.NewIPAddressRepo(ipdbIPDB, logger)
	conversationUsecase := usecase.NewConversationUsecase(conversationRepository, nodeRepository, geoRepo, logger, ipAddressRepo)
	modelUsecase := usecase.NewModelUsecase(modelRepository, nodeRepository, ragRepository, ragService, logger, configConfig, knowledgeBaseRepository)
	chatUsecase := usecase.NewChatUsecase(llmUsecase, conversationUsecase, modelUsecase, appRepository, logger)
	appUsecase := usecase.NewAppUsecase(appRepository, nodeUsecase, logger, configConfig, chatUsecase)
	appHandler := v1.NewAppHandler(echo, baseHandler, logger, authMiddleware, appUsecase, modelUsecase, conversationUsecase, configConfig)
	fileUsecase := usecase.NewFileUsecase(logger, minioClient, configConfig)
	fileHandler := v1.NewFileHandler(echo, baseHandler, logger, authMiddleware, minioClient, configConfig, fileUsecase)
	modelHandler := v1.NewModelHandler(echo, baseHandler, logger, authMiddleware, modelUsecase, llmUsecase)
	conversationHandler := v1.NewConversationHandler(echo, baseHandler, logger, authMiddleware, conversationUsecase)
	crawlerUsecase, err := usecase.NewCrawlerUsecase(logger)
	if err != nil {
		return nil, err
	}
	notionUseCase := usecase.NewNotionUsecase(logger, minioClient)
	epubUsecase := usecase.NewEpubUsecase(logger, minioClient)
	wikiJSUsecase := usecase.NewWikiJSUsecase(logger, fileUsecase)
	feishuUseCase := usecase.NewFeishuUseCase(logger, minioClient, crawlerUsecase)
	confluenceUsecase := usecase.NewConfluenceUsecase(logger, minioClient, crawlerUsecase, fileUsecase)
	crawlerHandler := v1.NewCrawlerHandler(echo, baseHandler, authMiddleware, logger, configConfig, crawlerUsecase, fileUsecase, notionUseCase, epubUsecase, wikiJSUsecase, feishuUseCase, confluenceUsecase)
	creationUsecase := usecase.NewCreationUsecase(logger, llmUsecase, modelUsecase)
	creationHandler := v1.NewCreationHandler(echo, baseHandler, logger, creationUsecase)
	statRepository := pg2.NewStatRepository(db)
	statUseCase := usecase.NewStatUseCase(statRepository, nodeRepository, conversationRepository, appRepository, ipAddressRepo, geoRepo, logger)
	statHandler := v1.NewStatHandler(baseHandler, echo, statUseCase, logger)
	commentRepository := pg2.NewCommentRepository(db, logger)
	commentUsecase := usecase.NewCommentUsecase(commentRepository, logger, nodeRepository, ipAddressRepo)
	commentHandler := v1.NewCommentHandler(echo, baseHandler, logger, authMiddleware, commentUsecase)
	licenseHandler := v1.NewLicenseHandler(echo, baseHandler)
	apiHandlers := &v1.APIHandlers{
		UserHandler:          userHandler,
		KnowledgeBaseHandler: knowledgeBaseHandler,
		NodeHandler:          nodeHandler,
		AppHandler:           appHandler,
		FileHandler:          fileHandler,
		ModelHandler:         modelHandler,
		ConversationHandler:  conversationHandler,
		CrawlerHandler:       crawlerHandler,
		CreationHandler:      creationHandler,
		StatHandler:          statHandler,
		CommentHandler:       commentHandler,
		LicenseHandler:       licenseHandler,
	}
	shareNodeHandler := share.NewShareNodeHandler(baseHandler, echo, nodeUsecase, logger)
	shareAppHandler := share.NewShareAppHandler(echo, baseHandler, logger, appUsecase)
	shareChatHandler := share.NewShareChatHandler(echo, baseHandler, logger, appUsecase, chatUsecase, conversationUsecase, modelUsecase)
	sitemapUsecase := usecase.NewSitemapUsecase(nodeRepository, knowledgeBaseRepository, logger)
	shareSitemapHandler := share.NewShareSitemapHandler(echo, baseHandler, sitemapUsecase, appUsecase, logger)
	shareStatHandler := share.NewShareStatHandler(baseHandler, echo, statUseCase, logger)
	shareCommentHandler := share.NewShareCommentHandler(echo, baseHandler, logger, commentUsecase, appUsecase)
	shareHandler := &share.ShareHandler{
		ShareNodeHandler:    shareNodeHandler,
		ShareAppHandler:     shareAppHandler,
		ShareChatHandler:    shareChatHandler,
		ShareSitemapHandler: shareSitemapHandler,
		ShareStatHandler:    shareStatHandler,
		ShareCommentHandler: shareCommentHandler,
	}
	client, err := telemetry.NewClient(logger, knowledgeBaseRepository)
	if err != nil {
		return nil, err
	}
	app := &App{
		HTTPServer:    httpServer,
		Handlers:      apiHandlers,
		ShareHandlers: shareHandler,
		Config:        configConfig,
		Logger:        logger,
		Telemetry:     client,
	}
	return app, nil
}

// wire.go:

type App struct {
	HTTPServer    *http.HTTPServer
	Handlers      *v1.APIHandlers
	ShareHandlers *share.ShareHandler
	Config        *config.Config
	Logger        *log.Logger
	Telemetry     *telemetry.Client
}
