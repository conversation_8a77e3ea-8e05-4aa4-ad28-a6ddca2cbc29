// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/chaitin/panda-wiki/config"
	mq2 "github.com/chaitin/panda-wiki/handler/mq"
	"github.com/chaitin/panda-wiki/log"
	"github.com/chaitin/panda-wiki/mq"
	pg2 "github.com/chaitin/panda-wiki/repo/pg"
	"github.com/chaitin/panda-wiki/store/pg"
	"github.com/chaitin/panda-wiki/store/rag"
	"github.com/chaitin/panda-wiki/usecase"
)

// Injectors from wire.go:

func createApp() (*App, error) {
	configConfig, err := config.NewConfig()
	if err != nil {
		return nil, err
	}
	logger := log.NewLogger(configConfig)
	mqConsumer, err := mq.NewMQConsumer(configConfig, logger)
	if err != nil {
		return nil, err
	}
	ragService, err := rag.NewRAGService(configConfig, logger)
	if err != nil {
		return nil, err
	}
	db, err := pg.NewDB(configConfig)
	if err != nil {
		return nil, err
	}
	nodeRepository := pg2.NewNodeRepository(db, logger)
	knowledgeBaseRepository := pg2.NewKnowledgeBaseRepository(db, configConfig, logger, ragService)
	conversationRepository := pg2.NewConversationRepository(db, logger)
	modelRepository := pg2.NewModelRepository(db, logger)
	llmUsecase := usecase.NewLLMUsecase(configConfig, ragService, conversationRepository, knowledgeBaseRepository, nodeRepository, modelRepository, logger)
	ragmqHandler, err := mq2.NewRAGMQHandler(mqConsumer, logger, ragService, nodeRepository, knowledgeBaseRepository, llmUsecase, modelRepository)
	if err != nil {
		return nil, err
	}
	statRepository := pg2.NewStatRepository(db)
	statCronHandler, err := mq2.NewStatCronHandler(logger, statRepository)
	if err != nil {
		return nil, err
	}
	mqHandlers := &mq2.MQHandlers{
		RAGMQHandler:    ragmqHandler,
		StatCronHandler: statCronHandler,
	}
	app := &App{
		MQConsumer:      mqConsumer,
		Config:          configConfig,
		MQHandlers:      mqHandlers,
		StatCronHandler: statCronHandler,
	}
	return app, nil
}

// wire.go:

type App struct {
	MQConsumer      mq.MQConsumer
	Config          *config.Config
	MQHandlers      *mq2.MQHandlers
	StatCronHandler *mq2.StatCronHandler
}
