// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/chaitin/panda-wiki/config"
	"github.com/chaitin/panda-wiki/log"
	"github.com/chaitin/panda-wiki/migration"
	"github.com/chaitin/panda-wiki/migration/fns"
	"github.com/chaitin/panda-wiki/mq"
	cache2 "github.com/chaitin/panda-wiki/repo/cache"
	mq2 "github.com/chaitin/panda-wiki/repo/mq"
	pg2 "github.com/chaitin/panda-wiki/repo/pg"
	"github.com/chaitin/panda-wiki/store/cache"
	"github.com/chaitin/panda-wiki/store/pg"
	"github.com/chaitin/panda-wiki/store/rag"
	"github.com/chaitin/panda-wiki/store/s3"
	"github.com/chaitin/panda-wiki/usecase"
)

// Injectors from wire.go:

func createApp() (*App, error) {
	configConfig, err := config.NewConfig()
	if err != nil {
		return nil, err
	}
	db, err := pg.NewDB(configConfig)
	if err != nil {
		return nil, err
	}
	logger := log.NewLogger(configConfig)
	nodeRepository := pg2.NewNodeRepository(db, logger)
	mqProducer, err := mq.NewMQProducer(configConfig, logger)
	if err != nil {
		return nil, err
	}
	ragRepository := mq2.NewRAGRepository(mqProducer)
	ragService, err := rag.NewRAGService(configConfig, logger)
	if err != nil {
		return nil, err
	}
	knowledgeBaseRepository := pg2.NewKnowledgeBaseRepository(db, configConfig, logger, ragService)
	conversationRepository := pg2.NewConversationRepository(db, logger)
	modelRepository := pg2.NewModelRepository(db, logger)
	llmUsecase := usecase.NewLLMUsecase(configConfig, ragService, conversationRepository, knowledgeBaseRepository, nodeRepository, modelRepository, logger)
	minioClient, err := s3.NewMinioClient(configConfig)
	if err != nil {
		return nil, err
	}
	nodeUsecase := usecase.NewNodeUsecase(nodeRepository, ragRepository, knowledgeBaseRepository, llmUsecase, logger, minioClient, modelRepository)
	cacheCache, err := cache.NewCache(configConfig)
	if err != nil {
		return nil, err
	}
	kbRepo := cache2.NewKBRepo(cacheCache)
	knowledgeBaseUsecase, err := usecase.NewKnowledgeBaseUsecase(knowledgeBaseRepository, nodeRepository, ragRepository, ragService, kbRepo, logger, configConfig)
	if err != nil {
		return nil, err
	}
	migrationNodeVersion := fns.NewMigrationNodeVersion(logger, nodeUsecase, knowledgeBaseUsecase, ragRepository)
	migrationFuncs := &migration.MigrationFuncs{
		NodeMigration: migrationNodeVersion,
	}
	manager, err := migration.NewManager(db, logger, migrationFuncs)
	if err != nil {
		return nil, err
	}
	app := &App{
		Config:           configConfig,
		MigrationManager: manager,
	}
	return app, nil
}

// wire.go:

type App struct {
	Config           *config.Config
	MigrationManager *migration.Manager
}
