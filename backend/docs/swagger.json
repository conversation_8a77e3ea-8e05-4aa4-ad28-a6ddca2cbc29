{"swagger": "2.0", "info": {"contact": {}}, "paths": {"/api/v1/app": {"put": {"description": "Update app", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["app"], "summary": "Update app", "parameters": [{"description": "app", "name": "app", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.UpdateAppReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}, "delete": {"description": "Delete app", "consumes": ["application/json"], "tags": ["app"], "summary": "Delete app", "parameters": [{"type": "string", "description": "app id", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/api/v1/app/detail": {"get": {"description": "Get app detail", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["app"], "summary": "Get app detail", "parameters": [{"type": "string", "description": "kb id", "name": "kb_id", "in": "query", "required": true}, {"type": "string", "description": "app type", "name": "type", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.AppDetailResp"}}}]}}}}}, "/api/v1/comment": {"get": {"description": "GetCommentList", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["comment"], "summary": "GetCommentList", "parameters": [{"type": "string", "name": "kb_id", "in": "query", "required": true}, {"minimum": 1, "type": "integer", "name": "page", "in": "query", "required": true}, {"minimum": 1, "type": "integer", "name": "per_page", "in": "query", "required": true}], "responses": {"200": {"description": "conversationList", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/v1.CommentLists"}}}]}}}}}, "/api/v1/comment/list": {"delete": {"description": "DeleteCommentList", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["comment"], "summary": "DeleteCommentList", "parameters": [{"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "name": "ids", "in": "query"}], "responses": {"200": {"description": "total", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/api/v1/conversation": {"get": {"description": "get conversation list", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["conversation"], "summary": "get conversation list", "parameters": [{"type": "string", "name": "app_id", "in": "query"}, {"type": "string", "name": "kb_id", "in": "query", "required": true}, {"minimum": 1, "type": "integer", "name": "page", "in": "query", "required": true}, {"minimum": 1, "type": "integer", "name": "per_page", "in": "query", "required": true}, {"type": "string", "name": "remote_ip", "in": "query"}, {"type": "string", "name": "subject", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/v1.ConversationListItems"}}}]}}}}}, "/api/v1/conversation/detail": {"get": {"description": "get conversation detail", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["conversation"], "summary": "get conversation detail", "parameters": [{"type": "string", "description": "user id", "name": "X-SafePoint-User-ID", "in": "header", "required": true}, {"type": "string", "description": "conversation id", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.ConversationDetailResp"}}}]}}}}}, "/api/v1/conversation/message/detail": {"get": {"description": "Get message detail", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Message"], "summary": "Get message detail", "parameters": [{"type": "string", "description": "message id", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.ConversationMessage"}}}]}}}}}, "/api/v1/conversation/message/list": {"get": {"description": "GetMessageFeedBackList", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Message"], "summary": "GetMessageFeedBackList", "parameters": [{"type": "string", "name": "kb_id", "in": "query", "required": true}, {"minimum": 1, "type": "integer", "name": "page", "in": "query", "required": true}, {"minimum": 1, "type": "integer", "name": "per_page", "in": "query", "required": true}], "responses": {"200": {"description": "MessageList", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.PaginatedResult-array_domain_ConversationMessageListItem"}}}]}}}}}, "/api/v1/crawler/confluence/analysis_export_file": {"post": {"description": "Analyze Confluence Export File", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["crawler"], "summary": "AnalysisConfluenceExportFile", "parameters": [{"type": "file", "description": "file", "name": "file", "in": "formData", "required": true}, {"type": "string", "description": "kb_id", "name": "kb_id", "in": "formData", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/domain.AnalysisConfluenceResp"}}}}]}}}}}, "/api/v1/crawler/epub/convert": {"post": {"description": "QpubConvert", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["crawler"], "summary": "QpubConvert", "parameters": [{"type": "file", "description": "file", "name": "file", "in": "formData", "required": true}, {"type": "string", "description": "kb_id", "name": "kb_id", "in": "formData", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.EpubResp"}}}]}}}}}, "/api/v1/crawler/feishu/get_doc": {"post": {"description": "Get Docx in Feishu Spaces", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["crawler"], "summary": "FeishuGetDocx", "parameters": [{"description": "Get Docx", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.GetDocxReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/domain.GetDocxResp"}}}}]}}}}}, "/api/v1/crawler/feishu/list_doc": {"post": {"description": "List Docx in Feishu Spaces", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["crawler"], "summary": "FeishuListDoc", "parameters": [{"description": "Search Docx", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.SearchDocxReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/domain.SearchDocxResp"}}}}]}}}}}, "/api/v1/crawler/feishu/list_spaces": {"post": {"description": "List All Feishu Spaces", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["crawler"], "summary": "FeishuListSpaces", "parameters": [{"description": "List Spaces", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.GetSpaceListReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/domain.GetSpaceListResp"}}}}]}}}}}, "/api/v1/crawler/feishu/search_wiki": {"post": {"description": "Search Wiki in Feishu Spaces", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["crawler"], "summary": "FeishuSearchWiki", "parameters": [{"description": "Search Wiki", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.SearchWikiReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/domain.SearchWikiResp"}}}}]}}}}}, "/api/v1/crawler/notion/get_doc": {"post": {"description": "GetDocs", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["crawler"], "summary": "GetDocs", "parameters": [{"description": "Get Docs", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.GetDocsReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/domain.Page"}}}}]}}}}}, "/api/v1/crawler/notion/get_list": {"post": {"description": "NotionGetList", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["crawler"], "summary": "NotionGetList", "parameters": [{"description": "Notion Get List", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.NotnionGetListReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/domain.PageInfo"}}}}]}}}}}, "/api/v1/crawler/parse_rss": {"post": {"description": "Parse RSS", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["crawler"], "summary": "Parse RSS", "parameters": [{"description": "Parse URL", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.ParseURLReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.ParseURLResp"}}}]}}}}}, "/api/v1/crawler/parse_sitemap": {"post": {"description": "Parse <PERSON>ma<PERSON>", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["crawler"], "summary": "Parse <PERSON>ma<PERSON>", "parameters": [{"description": "Parse URL", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.ParseURLReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.ParseURLResp"}}}]}}}}}, "/api/v1/crawler/scrape": {"post": {"description": "Scrape", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["crawler"], "summary": "Scrape", "parameters": [{"description": "Scrape", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.ScrapeReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.ScrapeResp"}}}]}}}}}, "/api/v1/crawler/wikijs/analysis_export_file": {"post": {"description": "AnalysisWikijsExportFile", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["crawler"], "summary": "AnalysisWikijsExportFile", "parameters": [{"type": "file", "description": "file", "name": "file", "in": "formData", "required": true}, {"type": "string", "description": "kb_id", "name": "kb_id", "in": "formData", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/domain.WikiJSResp"}}}}]}}}}}, "/api/v1/creation/text": {"post": {"description": "Text creation", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["creation"], "summary": "Text creation", "parameters": [{"description": "text creation request", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.TextReq"}}], "responses": {"200": {"description": "success", "schema": {"type": "string"}}}}}, "/api/v1/file/upload": {"post": {"description": "Upload File", "consumes": ["multipart/form-data"], "tags": ["file"], "summary": "Upload File", "parameters": [{"type": "file", "description": "File", "name": "file", "in": "formData", "required": true}, {"type": "string", "description": "Knowledge Base ID", "name": "kb_id", "in": "formData"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.ObjectUploadResp"}}}}}, "/api/v1/knowledge_base": {"post": {"description": "CreateKnowledgeBase", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["knowledge_base"], "summary": "CreateKnowledgeBase", "parameters": [{"description": "CreateKnowledgeBase Request", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.CreateKnowledgeBaseReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/api/v1/knowledge_base/detail": {"get": {"description": "GetKnowledgeBaseDetail", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["knowledge_base"], "summary": "GetKnowledgeBaseDetail", "parameters": [{"type": "string", "description": "Knowledge Base ID", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.KnowledgeBaseDetail"}}}]}}}}, "put": {"description": "UpdateKnowledgeBase", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["knowledge_base"], "summary": "UpdateKnowledgeBase", "parameters": [{"description": "UpdateKnowledgeBase Request", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.UpdateKnowledgeBaseReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}, "delete": {"description": "DeleteKnowledgeBase", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["knowledge_base"], "summary": "DeleteKnowledgeBase", "parameters": [{"type": "string", "description": "Knowledge Base ID", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/api/v1/knowledge_base/list": {"get": {"description": "GetKnowledgeBaseList", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["knowledge_base"], "summary": "GetKnowledgeBaseList", "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/domain.KnowledgeBaseListItem"}}}}]}}}}}, "/api/v1/knowledge_base/release": {"post": {"description": "CreateKBRelease", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["knowledge_base"], "summary": "CreateKBRelease", "parameters": [{"description": "CreateKBRelease Request", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.CreateKBReleaseReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/api/v1/knowledge_base/release/list": {"get": {"description": "GetKBReleaseList", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["knowledge_base"], "summary": "GetKBReleaseList", "parameters": [{"type": "string", "description": "Knowledge Base ID", "name": "kb_id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.GetKBReleaseListResp"}}}]}}}}}, "/api/v1/model": {"put": {"description": "update model", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["model"], "parameters": [{"description": "update model request", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.UpdateModelReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}, "post": {"description": "create model", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["model"], "summary": "create model", "parameters": [{"description": "create model request", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.CreateModelReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/api/v1/model/check": {"post": {"description": "check model", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["model"], "summary": "check model", "parameters": [{"description": "check model request", "name": "model", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.CheckModelReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.CheckModelResp"}}}]}}}}}, "/api/v1/model/detail": {"get": {"description": "get model detail", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["model"], "summary": "get model detail", "parameters": [{"type": "string", "description": "model id", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.ModelDetailResp"}}}]}}}}}, "/api/v1/model/list": {"get": {"description": "get model list", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["model"], "summary": "get model list", "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.ModelListItem"}}}]}}}}}, "/api/v1/model/provider/supported": {"get": {"description": "get provider supported model list", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["model"], "summary": "get provider supported model list", "parameters": [{"type": "string", "name": "api_header", "in": "query"}, {"type": "string", "name": "api_key", "in": "query"}, {"type": "string", "name": "base_url", "in": "query", "required": true}, {"enum": ["SiliconFlow", "OpenAI", "Ollama", "DeepSeek", "Moonshot", "AzureOpenAI", "BaiZhiCloud", "Hunyuan", "<PERSON><PERSON><PERSON>", "Volcengine", "Gemini"], "type": "string", "name": "provider", "in": "query", "required": true}, {"enum": ["chat", "embedding", "rerank"], "type": "string", "x-enum-varnames": ["ModelTypeChat", "ModelTypeEmbedding", "ModelTypeRerank"], "name": "type", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.GetProviderModelListResp"}}}]}}}}}, "/api/v1/node": {"post": {"description": "Create Node", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["node"], "summary": "Create Node", "parameters": [{"description": "Node", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.CreateNodeReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "string"}}}}]}}}}}, "/api/v1/node/action": {"post": {"description": "Node Action", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["node"], "summary": "Node Action", "parameters": [{"description": "Action", "name": "action", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.NodeActionReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "string"}}}}]}}}}}, "/api/v1/node/batch_move": {"post": {"description": "Batch Move Node", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["node"], "summary": "Batch Move Node", "parameters": [{"description": "Batch Move Node", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.BatchMoveReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/api/v1/node/detail": {"get": {"description": "Get Node Detail", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["node"], "summary": "Get Node Detail", "parameters": [{"type": "string", "description": "ID", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.NodeDetailResp"}}}]}}}}, "put": {"description": "Update Node Detail", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["node"], "summary": "Update Node Detail", "parameters": [{"description": "Node", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.UpdateNodeReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/api/v1/node/list": {"get": {"description": "Get Node List", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["node"], "summary": "Get Node List", "parameters": [{"type": "string", "name": "kb_id", "in": "query", "required": true}, {"type": "string", "name": "search", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/domain.NodeListItemResp"}}}}]}}}}}, "/api/v1/node/move": {"post": {"description": "Move Node", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["node"], "summary": "Move Node", "parameters": [{"description": "Move Node", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.MoveNodeReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/api/v1/node/recommend_nodes": {"get": {"description": "Recommend <PERSON>des", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["node"], "summary": "Recommend <PERSON>des", "parameters": [{"type": "string", "name": "kb_id", "in": "query", "required": true}, {"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "name": "node_ids", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/domain.RecommendNodeListResp"}}}}]}}}}}, "/api/v1/node/release/detail": {"get": {"description": "Get Node Release Detail", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["node"], "summary": "Get Node Release Detail", "parameters": [{"type": "string", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.GetNodeReleaseDetailResp"}}}]}}}}}, "/api/v1/node/release/list": {"get": {"description": "Get Node Release List", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["node"], "summary": "Get Node Release List", "parameters": [{"type": "string", "name": "kb_id", "in": "query", "required": true}, {"type": "string", "name": "node_id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/domain.NodeReleaseListItem"}}}}]}}}}}, "/api/v1/node/summary": {"post": {"description": "Summary <PERSON>de", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["node"], "summary": "Summary <PERSON>de", "parameters": [{"description": "Summary <PERSON>de", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.NodeSummaryReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/api/v1/stat/browsers": {"get": {"description": "GetBrowsers", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["stat"], "summary": "GetBrowsers", "parameters": [{"type": "string", "description": "kb_id", "name": "kb_id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/api/v1/stat/conversation_distribution": {"get": {"description": "GetConversationDistribution", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["stat"], "summary": "GetConversationDistribution", "parameters": [{"type": "string", "description": "kb_id", "name": "kb_id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/api/v1/stat/count": {"get": {"description": "GetCount", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["stat"], "summary": "GetCount", "parameters": [{"type": "string", "description": "kb_id", "name": "kb_id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/api/v1/stat/geo_count": {"get": {"description": "GetGeoCount", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["stat"], "summary": "GetGeoCount", "parameters": [{"type": "string", "description": "kb_id", "name": "kb_id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/api/v1/stat/hot_pages": {"get": {"description": "GetHotPages", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["stat"], "summary": "GetHotPages", "parameters": [{"type": "string", "description": "kb_id", "name": "kb_id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/api/v1/stat/instant_count": {"get": {"description": "GetInstantCount", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["stat"], "summary": "GetInstantCount", "parameters": [{"type": "string", "description": "kb_id", "name": "kb_id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/api/v1/stat/instant_pages": {"get": {"description": "GetInstantPages", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["stat"], "summary": "GetInstantPages", "parameters": [{"type": "string", "description": "kb_id", "name": "kb_id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/api/v1/stat/referer_hosts": {"get": {"description": "GetRefererHosts", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["stat"], "summary": "GetRefererHosts", "parameters": [{"type": "string", "description": "kb_id", "name": "kb_id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/api/v1/user": {"get": {"description": "GetUser", "consumes": ["application/json"], "tags": ["user"], "summary": "GetUser", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.UserInfoResp"}}}}}, "/api/v1/user/create": {"post": {"description": "CreateUser", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "CreateUser", "parameters": [{"description": "CreateUser Request", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.CreateUserReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/api/v1/user/delete": {"delete": {"description": "DeleteUser", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "DeleteUser", "parameters": [{"description": "DeleteUser Request", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.DeleteUserReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/api/v1/user/list": {"get": {"description": "ListUsers", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "ListUsers", "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/domain.UserListItemResp"}}}}]}}}}}, "/api/v1/user/login": {"post": {"description": "<PERSON><PERSON>", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "<PERSON><PERSON>", "parameters": [{"description": "Login Request", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.LoginReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.LoginResp"}}}}}, "/api/v1/user/reset_password": {"put": {"description": "ResetPassword", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "ResetPassword", "parameters": [{"description": "ResetPassword Request", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.ResetPasswordReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/share/v1/app/web/info": {"get": {"description": "GetAppInfo", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["share_app"], "summary": "GetAppInfo", "parameters": [{"type": "string", "description": "kb id", "name": "X-KB-ID", "in": "header", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/share/v1/app/widget/info": {"get": {"description": "GetWidgetAppInfo", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["share_app"], "summary": "GetWidgetAppInfo", "parameters": [{"type": "string", "description": "kb id", "name": "X-KB-ID", "in": "header", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/share/v1/chat/feedback": {"post": {"description": "Process user feedback for chat conversations", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["share_chat"], "summary": "Handle chat feedback", "parameters": [{"description": "feedback request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.FeedbackRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/share/v1/chat/message": {"post": {"description": "ChatMessage", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["share_chat"], "summary": "ChatMessage", "parameters": [{"type": "string", "description": "app type", "name": "app_type", "in": "query", "required": true}, {"description": "request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.ChatRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/share/v1/chat/widget": {"post": {"description": "ChatWidget", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["share_chat"], "summary": "ChatWidget", "parameters": [{"type": "string", "description": "app type", "name": "app_type", "in": "query", "required": true}, {"description": "request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.ChatRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/share/v1/comment": {"post": {"description": "CreateComment", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["share_comment"], "summary": "CreateComment", "parameters": [{"description": "Comment", "name": "comment", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.CommentReq"}}], "responses": {"200": {"description": "CommentID", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"type": "string"}}}]}}}}}, "/share/v1/comment/list": {"get": {"description": "GetCommentList", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["share_comment"], "summary": "GetCommentList", "parameters": [{"type": "string", "description": "nodeID", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "CommentList", "schema": {"allOf": [{"$ref": "#/definitions/domain.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/share.ShareCommentLists"}}}]}}}}}, "/share/v1/node/detail": {"get": {"description": "GetNodeDetail", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["share_node"], "summary": "GetNodeDetail", "parameters": [{"type": "string", "description": "kb id", "name": "X-KB-ID", "in": "header", "required": true}, {"type": "string", "description": "node id", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/share/v1/node/list": {"get": {"description": "GetNodeList", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["share_node"], "summary": "GetNodeList", "parameters": [{"type": "string", "description": "kb id", "name": "X-KB-ID", "in": "header", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}, "/share/v1/stat/page": {"post": {"description": "RecordPage", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["share_stat"], "summary": "RecordPage", "parameters": [{"description": "request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.StatPageReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.Response"}}}}}}, "definitions": {"domain.AccessSettings": {"type": "object", "properties": {"base_url": {"type": "string"}, "hosts": {"type": "array", "items": {"type": "string"}}, "ports": {"type": "array", "items": {"type": "integer"}}, "private_key": {"type": "string"}, "public_key": {"type": "string"}, "simple_auth": {"$ref": "#/definitions/domain.SimpleAuth"}, "ssl_ports": {"type": "array", "items": {"type": "integer"}}, "trusted_proxies": {"type": "array", "items": {"type": "string"}}}}, "domain.AnalysisConfluenceResp": {"type": "object", "properties": {"content": {"type": "string"}, "id": {"type": "string"}, "title": {"type": "string"}}}, "domain.AppDetailResp": {"type": "object", "properties": {"id": {"type": "string"}, "kb_id": {"type": "string"}, "name": {"type": "string"}, "recommend_nodes": {"type": "array", "items": {"$ref": "#/definitions/domain.RecommendNodeListResp"}}, "settings": {"$ref": "#/definitions/domain.AppSettingsResp"}, "type": {"$ref": "#/definitions/domain.AppType"}}}, "domain.AppSettings": {"type": "object", "properties": {"auto_sitemap": {"type": "boolean"}, "body_code": {"type": "string"}, "btns": {"type": "array", "items": {}}, "catalog_settings": {"description": "catalog settings", "allOf": [{"$ref": "#/definitions/domain.CatalogSettings"}]}, "desc": {"description": "seo", "type": "string"}, "dingtalk_bot_client_id": {"type": "string"}, "dingtalk_bot_client_secret": {"type": "string"}, "dingtalk_bot_is_enabled": {"description": "DingTalkBot", "type": "boolean"}, "dingtalk_bot_template_id": {"type": "string"}, "discord_bot_is_enabled": {"description": "DisCordBot", "type": "boolean"}, "discord_bot_token": {"type": "string"}, "feishu_bot_app_id": {"type": "string"}, "feishu_bot_app_secret": {"type": "string"}, "feishu_bot_is_enabled": {"description": "FeishuBot", "type": "boolean"}, "footer_settings": {"description": "footer settings", "allOf": [{"$ref": "#/definitions/domain.FooterSettings"}]}, "head_code": {"description": "inject code", "type": "string"}, "icon": {"type": "string"}, "keyword": {"type": "string"}, "recommend_node_ids": {"type": "array", "items": {"type": "string"}}, "recommend_questions": {"type": "array", "items": {"type": "string"}}, "search_placeholder": {"type": "string"}, "theme_and_style": {"$ref": "#/definitions/domain.ThemeAndStyle"}, "theme_mode": {"description": "theme", "type": "string"}, "title": {"description": "nav", "type": "string"}, "web_app_comment_settings": {"description": "webapp comment settings", "allOf": [{"$ref": "#/definitions/domain.WebAppCommentSettings"}]}, "wechat_app_agent_id": {"type": "string"}, "wechat_app_corpid": {"type": "string"}, "wechat_app_encodingaeskey": {"type": "string"}, "wechat_app_is_enabled": {"description": "WechatAppBot", "type": "boolean"}, "wechat_app_secret": {"type": "string"}, "wechat_app_token": {"type": "string"}, "wechat_official_account_app_id": {"type": "string"}, "wechat_official_account_app_secret": {"type": "string"}, "wechat_official_account_encodingaeskey": {"type": "string"}, "wechat_official_account_is_enabled": {"description": "<PERSON><PERSON>tOfficialAccount", "type": "boolean"}, "wechat_official_account_token": {"type": "string"}, "wechat_service_corpid": {"type": "string"}, "wechat_service_encodingaeskey": {"type": "string"}, "wechat_service_is_enabled": {"description": "WechatServiceBot", "type": "boolean"}, "wechat_service_secret": {"type": "string"}, "wechat_service_token": {"type": "string"}, "welcome_str": {"description": "welcome", "type": "string"}, "widget_bot_settings": {"description": "Widget bot settings", "allOf": [{"$ref": "#/definitions/domain.WidgetBotSettings"}]}}}, "domain.AppSettingsResp": {"type": "object", "properties": {"auto_sitemap": {"type": "boolean"}, "body_code": {"type": "string"}, "btns": {"type": "array", "items": {}}, "catalog_settings": {"description": "catalog settings", "allOf": [{"$ref": "#/definitions/domain.CatalogSettings"}]}, "desc": {"description": "seo", "type": "string"}, "dingtalk_bot_client_id": {"type": "string"}, "dingtalk_bot_client_secret": {"type": "string"}, "dingtalk_bot_is_enabled": {"description": "DingTalkBot", "type": "boolean"}, "dingtalk_bot_template_id": {"type": "string"}, "discord_bot_is_enabled": {"description": "DisCordBot", "type": "boolean"}, "discord_bot_token": {"type": "string"}, "feishu_bot_app_id": {"type": "string"}, "feishu_bot_app_secret": {"type": "string"}, "feishu_bot_is_enabled": {"description": "FeishuBot", "type": "boolean"}, "footer_settings": {"description": "footer settings", "allOf": [{"$ref": "#/definitions/domain.FooterSettings"}]}, "head_code": {"description": "inject code", "type": "string"}, "icon": {"type": "string"}, "keyword": {"type": "string"}, "recommend_node_ids": {"type": "array", "items": {"type": "string"}}, "recommend_questions": {"type": "array", "items": {"type": "string"}}, "search_placeholder": {"type": "string"}, "theme_and_style": {"$ref": "#/definitions/domain.ThemeAndStyle"}, "theme_mode": {"description": "theme", "type": "string"}, "title": {"description": "nav", "type": "string"}, "web_app_comment_settings": {"description": "webapp comment settings", "allOf": [{"$ref": "#/definitions/domain.WebAppCommentSettings"}]}, "wechat_app_agent_id": {"type": "string"}, "wechat_app_corpid": {"type": "string"}, "wechat_app_encodingaeskey": {"type": "string"}, "wechat_app_is_enabled": {"description": "WechatAppBot", "type": "boolean"}, "wechat_app_secret": {"type": "string"}, "wechat_app_token": {"type": "string"}, "wechat_official_account_app_id": {"type": "string"}, "wechat_official_account_app_secret": {"type": "string"}, "wechat_official_account_encodingaeskey": {"type": "string"}, "wechat_official_account_is_enabled": {"description": "<PERSON><PERSON>tOfficialAccount", "type": "boolean"}, "wechat_official_account_token": {"type": "string"}, "wechat_service_corpid": {"type": "string"}, "wechat_service_encodingaeskey": {"type": "string"}, "wechat_service_is_enabled": {"description": "WechatServiceBot", "type": "boolean"}, "wechat_service_secret": {"type": "string"}, "wechat_service_token": {"type": "string"}, "welcome_str": {"description": "welcome", "type": "string"}, "widget_bot_settings": {"description": "WidgetBot", "allOf": [{"$ref": "#/definitions/domain.WidgetBotSettings"}]}}}, "domain.AppType": {"type": "integer", "enum": [1, 2, 3, 4, 5, 6, 7, 8], "x-enum-varnames": ["AppTypeWeb", "AppTypeWidget", "AppTypeDingTalkBot", "AppTypeFeishuBot", "AppTypeWechatBot", "AppTypeWechatServiceBot", "AppTypeDisCordBot", "AppTypeWechatOfficialAccount"]}, "domain.BatchMoveReq": {"type": "object", "required": ["ids", "kb_id"], "properties": {"ids": {"type": "array", "items": {"type": "string"}}, "kb_id": {"type": "string"}, "parent_id": {"type": "string"}}}, "domain.BrandGroup": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/definitions/domain.Link"}}, "name": {"type": "string"}}}, "domain.CatalogSettings": {"type": "object", "properties": {"catalog_folder": {"description": "1: 展开, 2: 折叠, default: 1", "type": "integer"}, "catalog_visible": {"description": "1: 显示, 2: 隐藏, default: 1", "type": "integer"}, "catalog_width": {"description": "200 - 300, default: 260", "type": "integer"}}}, "domain.ChatRequest": {"type": "object", "required": ["app_type", "message"], "properties": {"app_type": {"enum": [1, 2], "allOf": [{"$ref": "#/definitions/domain.AppType"}]}, "conversation_id": {"type": "string"}, "message": {"type": "string"}, "nonce": {"type": "string"}}}, "domain.CheckModelReq": {"type": "object", "required": ["base_url", "model", "provider", "type"], "properties": {"api_header": {"type": "string"}, "api_key": {"type": "string"}, "api_version": {"description": "for azure openai", "type": "string"}, "base_url": {"type": "string"}, "model": {"type": "string"}, "provider": {"enum": ["OpenAI", "Ollama", "DeepSeek", "SiliconFlow", "Moonshot", "Other", "AzureOpenAI", "BaiZhiCloud", "Hunyuan", "<PERSON><PERSON><PERSON>", "Volcengine", "Gemini"], "allOf": [{"$ref": "#/definitions/domain.ModelProvider"}]}, "type": {"enum": ["chat", "embedding", "rerank"], "allOf": [{"$ref": "#/definitions/domain.ModelType"}]}}}, "domain.CheckModelResp": {"type": "object", "properties": {"content": {"type": "string"}, "error": {"type": "string"}}}, "domain.CommentInfo": {"type": "object", "properties": {"remote_ip": {"type": "string"}, "user_name": {"type": "string"}}}, "domain.CommentListItem": {"type": "object", "properties": {"content": {"type": "string"}, "created_at": {"type": "string"}, "id": {"type": "string"}, "info": {"$ref": "#/definitions/domain.CommentInfo"}, "ip_address": {"description": "ip地址", "allOf": [{"$ref": "#/definitions/domain.IPAddress"}]}, "node_id": {"type": "string"}, "node_name": {"description": "文档标题", "type": "string"}, "node_type": {"type": "integer"}, "root_id": {"type": "string"}}}, "domain.CommentReq": {"type": "object", "required": ["content", "node_id"], "properties": {"content": {"type": "string"}, "node_id": {"type": "string"}, "parent_id": {"type": "string"}, "root_id": {"type": "string"}, "user_name": {"type": "string"}}}, "domain.ConversationDetailResp": {"type": "object", "properties": {"app_id": {"type": "string"}, "created_at": {"type": "string"}, "id": {"type": "string"}, "ip_address": {"$ref": "#/definitions/domain.IPAddress"}, "messages": {"type": "array", "items": {"$ref": "#/definitions/domain.ConversationMessage"}}, "references": {"type": "array", "items": {"$ref": "#/definitions/domain.ConversationReference"}}, "remote_ip": {"type": "string"}, "subject": {"type": "string"}}}, "domain.ConversationInfo": {"type": "object", "properties": {"user_info": {"$ref": "#/definitions/domain.UserInfo"}}}, "domain.ConversationListItem": {"type": "object", "properties": {"app_name": {"type": "string"}, "app_type": {"$ref": "#/definitions/domain.AppType"}, "created_at": {"type": "string"}, "feedback_info": {"description": "用户反馈信息", "allOf": [{"$ref": "#/definitions/domain.FeedBackInfo"}]}, "id": {"type": "string"}, "info": {"description": "用户信息", "allOf": [{"$ref": "#/definitions/domain.ConversationInfo"}]}, "ip_address": {"$ref": "#/definitions/domain.IPAddress"}, "remote_ip": {"type": "string"}, "subject": {"type": "string"}}}, "domain.ConversationMessage": {"type": "object", "properties": {"app_id": {"type": "string"}, "completion_tokens": {"type": "integer"}, "content": {"type": "string"}, "conversation_id": {"type": "string"}, "created_at": {"type": "string"}, "id": {"type": "string"}, "info": {"description": "feedbackinfo", "allOf": [{"$ref": "#/definitions/domain.FeedBackInfo"}]}, "kb_id": {"type": "string"}, "model": {"type": "string"}, "parent_id": {"description": "parent_id", "type": "string"}, "prompt_tokens": {"type": "integer"}, "provider": {"description": "model", "allOf": [{"$ref": "#/definitions/domain.ModelProvider"}]}, "remote_ip": {"description": "stats", "type": "string"}, "role": {"$ref": "#/definitions/schema.RoleType"}, "total_tokens": {"type": "integer"}}}, "domain.ConversationMessageListItem": {"type": "object", "properties": {"app_id": {"type": "string"}, "app_type": {"$ref": "#/definitions/domain.AppType"}, "conversation_id": {"type": "string"}, "conversation_info": {"description": "userInfo", "allOf": [{"$ref": "#/definitions/domain.ConversationInfo"}]}, "created_at": {"type": "string"}, "id": {"type": "string"}, "info": {"description": "feedbackInfo", "allOf": [{"$ref": "#/definitions/domain.FeedBackInfo"}]}, "ip_address": {"$ref": "#/definitions/domain.IPAddress"}, "question": {"type": "string"}, "remote_ip": {"description": "stats", "type": "string"}}}, "domain.ConversationReference": {"type": "object", "properties": {"app_id": {"type": "string"}, "conversation_id": {"type": "string"}, "name": {"type": "string"}, "node_id": {"type": "string"}, "url": {"type": "string"}}}, "domain.CreateKBReleaseReq": {"type": "object", "required": ["kb_id", "message", "tag"], "properties": {"kb_id": {"type": "string"}, "message": {"type": "string"}, "node_ids": {"description": "create release after these nodes published", "type": "array", "items": {"type": "string"}}, "tag": {"type": "string"}}}, "domain.CreateKnowledgeBaseReq": {"type": "object", "required": ["name"], "properties": {"hosts": {"type": "array", "items": {"type": "string"}}, "name": {"type": "string"}, "ports": {"type": "array", "items": {"type": "integer"}}, "private_key": {"type": "string"}, "public_key": {"type": "string"}, "ssl_ports": {"type": "array", "items": {"type": "integer"}}}}, "domain.CreateModelReq": {"type": "object", "required": ["base_url", "model", "provider", "type"], "properties": {"api_header": {"type": "string"}, "api_key": {"type": "string"}, "api_version": {"description": "for azure openai", "type": "string"}, "base_url": {"type": "string"}, "model": {"type": "string"}, "provider": {"enum": ["OpenAI", "Ollama", "DeepSeek", "SiliconFlow", "Moonshot", "Other", "AzureOpenAI", "BaiZhiCloud", "Hunyuan", "<PERSON><PERSON><PERSON>", "Volcengine", "Gemini"], "allOf": [{"$ref": "#/definitions/domain.ModelProvider"}]}, "type": {"enum": ["chat", "embedding", "rerank"], "allOf": [{"$ref": "#/definitions/domain.ModelType"}]}}}, "domain.CreateNodeReq": {"type": "object", "required": ["kb_id", "name", "type"], "properties": {"content": {"type": "string"}, "emoji": {"type": "string"}, "kb_id": {"type": "string"}, "name": {"type": "string"}, "parent_id": {"type": "string"}, "type": {"enum": [1, 2], "allOf": [{"$ref": "#/definitions/domain.NodeType"}]}, "visibility": {"$ref": "#/definitions/domain.NodeVisibility"}}}, "domain.CreateUserReq": {"type": "object", "required": ["account", "password"], "properties": {"account": {"type": "string"}, "password": {"type": "string", "minLength": 8}}}, "domain.DeleteUserReq": {"type": "object", "required": ["user_id"], "properties": {"user_id": {"type": "string"}}}, "domain.EpubResp": {"type": "object", "properties": {"content": {"type": "string"}, "title": {"type": "string"}}}, "domain.FeedBackInfo": {"type": "object", "properties": {"feedback_content": {"type": "string"}, "feedback_type": {"$ref": "#/definitions/domain.FeedbackType"}, "score": {"$ref": "#/definitions/domain.ScoreType"}}}, "domain.FeedbackRequest": {"type": "object", "required": ["message_id"], "properties": {"conversation_id": {"type": "string"}, "feedback_content": {"description": "限制内容长度", "type": "string", "maxLength": 200}, "message_id": {"type": "string"}, "score": {"description": "-1 踩 ,0 1 赞成", "allOf": [{"$ref": "#/definitions/domain.ScoreType"}]}, "type": {"description": "1 内容不准确，2 没有帮助，3 其他", "allOf": [{"$ref": "#/definitions/domain.FeedbackType"}]}}}, "domain.FeedbackType": {"type": "integer", "enum": [1, 2, 3], "x-enum-varnames": ["ContentErr", "NoHelp", "Other"]}, "domain.FooterSettings": {"type": "object", "properties": {"brand_desc": {"type": "string"}, "brand_groups": {"type": "array", "items": {"$ref": "#/definitions/domain.BrandGroup"}}, "brand_logo": {"type": "string"}, "brand_name": {"type": "string"}, "corp_name": {"type": "string"}, "footer_style": {"type": "string"}, "icp": {"type": "string"}}}, "domain.GetDocsReq": {"type": "object", "required": ["kb_id"], "properties": {"integration": {"type": "string"}, "kb_id": {"type": "string"}, "pages": {"type": "array", "items": {"$ref": "#/definitions/domain.PageInfo"}}}}, "domain.GetDocxReq": {"type": "object", "required": ["kb_id", "sources"], "properties": {"app_id": {"type": "string"}, "app_secret": {"type": "string"}, "kb_id": {"type": "string"}, "sources": {"type": "array", "items": {"$ref": "#/definitions/domain.Source"}}, "user_access_token": {"type": "string"}}}, "domain.GetDocxResp": {"type": "object", "properties": {"content": {"type": "string"}, "title": {"type": "string"}}}, "domain.GetKBReleaseListResp": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/domain.KBReleaseListItemResp"}}, "total": {"type": "integer"}}}, "domain.GetNodeReleaseDetailResp": {"type": "object", "properties": {"content": {"type": "string"}, "meta": {"$ref": "#/definitions/domain.NodeMeta"}, "name": {"type": "string"}}}, "domain.GetProviderModelListResp": {"type": "object", "properties": {"models": {"type": "array", "items": {"$ref": "#/definitions/domain.ProviderModelListItem"}}}}, "domain.GetSpaceListReq": {"type": "object", "properties": {"app_id": {"type": "string"}, "app_secret": {"type": "string"}, "user_access_token": {"type": "string"}}}, "domain.GetSpaceListResp": {"type": "object", "properties": {"name": {"type": "string"}, "space_id": {"type": "string"}}}, "domain.IPAddress": {"type": "object", "properties": {"city": {"type": "string"}, "country": {"type": "string"}, "ip": {"type": "string"}, "province": {"type": "string"}}}, "domain.KBReleaseListItemResp": {"type": "object", "properties": {"created_at": {"type": "string"}, "id": {"type": "string"}, "kb_id": {"type": "string"}, "message": {"type": "string"}, "tag": {"type": "string"}}}, "domain.KnowledgeBaseDetail": {"type": "object", "properties": {"access_settings": {"$ref": "#/definitions/domain.AccessSettings"}, "created_at": {"type": "string"}, "dataset_id": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "updated_at": {"type": "string"}}}, "domain.KnowledgeBaseListItem": {"type": "object", "properties": {"access_settings": {"$ref": "#/definitions/domain.AccessSettings"}, "created_at": {"type": "string"}, "dataset_id": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "updated_at": {"type": "string"}}}, "domain.Link": {"type": "object", "properties": {"name": {"type": "string"}, "url": {"type": "string"}}}, "domain.LoginReq": {"type": "object", "required": ["account", "password"], "properties": {"account": {"type": "string"}, "password": {"type": "string"}}}, "domain.LoginResp": {"type": "object", "properties": {"token": {"type": "string"}}}, "domain.MessageFrom": {"type": "integer", "enum": [1, 2], "x-enum-varnames": ["MessageFromGroup", "MessageFromPrivate"]}, "domain.ModelDetailResp": {"type": "object", "properties": {"api_header": {"type": "string"}, "api_key": {"type": "string"}, "api_version": {"description": "for azure openai", "type": "string"}, "base_url": {"type": "string"}, "completion_tokens": {"type": "integer"}, "created_at": {"type": "string"}, "id": {"type": "string"}, "model": {"type": "string"}, "prompt_tokens": {"type": "integer"}, "provider": {"$ref": "#/definitions/domain.ModelProvider"}, "total_tokens": {"type": "integer"}, "type": {"$ref": "#/definitions/domain.ModelType"}, "updated_at": {"type": "string"}}}, "domain.ModelListItem": {"type": "object", "properties": {"api_header": {"type": "string"}, "api_key": {"type": "string"}, "api_version": {"description": "for azure openai", "type": "string"}, "base_url": {"type": "string"}, "completion_tokens": {"type": "integer"}, "id": {"type": "string"}, "model": {"type": "string"}, "prompt_tokens": {"type": "integer"}, "provider": {"$ref": "#/definitions/domain.ModelProvider"}, "total_tokens": {"type": "integer"}, "type": {"$ref": "#/definitions/domain.ModelType"}}}, "domain.ModelProvider": {"type": "string", "enum": ["OpenAI", "Ollama", "DeepSeek", "Moonshot", "SiliconFlow", "AzureOpenAI", "BaiZhiCloud", "Hunyuan", "<PERSON><PERSON><PERSON>", "Volcengine", "Gemini", "Other"], "x-enum-varnames": ["ModelProviderBrandOpenAI", "ModelProviderBrandOllama", "ModelProviderBrandDeepSeek", "ModelProviderBrandMoonshot", "ModelProviderBrandSiliconFlow", "ModelProviderBrandAzureOpenAI", "ModelProviderBrandBaiZhiCloud", "ModelProviderBrandHunyuan", "ModelProviderBrandBaiLian", "ModelProviderBrandVolcengine", "ModelProviderBrandGemini", "ModelProviderBrandOther"]}, "domain.ModelType": {"type": "string", "enum": ["chat", "embedding", "rerank"], "x-enum-varnames": ["ModelTypeChat", "ModelTypeEmbedding", "ModelTypeRerank"]}, "domain.MoveNodeReq": {"type": "object", "required": ["id"], "properties": {"id": {"type": "string"}, "next_id": {"type": "string"}, "parent_id": {"type": "string"}, "prev_id": {"type": "string"}}}, "domain.NodeActionReq": {"type": "object", "required": ["action", "ids", "kb_id"], "properties": {"action": {"type": "string", "enum": ["delete", "private", "public"]}, "ids": {"type": "array", "items": {"type": "string"}}, "kb_id": {"type": "string"}}}, "domain.NodeDetailResp": {"type": "object", "properties": {"content": {"type": "string"}, "created_at": {"type": "string"}, "id": {"type": "string"}, "kb_id": {"type": "string"}, "meta": {"$ref": "#/definitions/domain.NodeMeta"}, "name": {"type": "string"}, "parent_id": {"type": "string"}, "status": {"$ref": "#/definitions/domain.NodeStatus"}, "type": {"$ref": "#/definitions/domain.NodeType"}, "updated_at": {"type": "string"}, "visibility": {"$ref": "#/definitions/domain.NodeVisibility"}}}, "domain.NodeListItemResp": {"type": "object", "properties": {"created_at": {"type": "string"}, "emoji": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "parent_id": {"type": "string"}, "position": {"type": "number"}, "status": {"$ref": "#/definitions/domain.NodeStatus"}, "summary": {"type": "string"}, "type": {"$ref": "#/definitions/domain.NodeType"}, "updated_at": {"type": "string"}, "visibility": {"$ref": "#/definitions/domain.NodeVisibility"}}}, "domain.NodeMeta": {"type": "object", "properties": {"emoji": {"type": "string"}, "summary": {"type": "string"}}}, "domain.NodeReleaseListItem": {"type": "object", "properties": {"id": {"type": "string"}, "meta": {"$ref": "#/definitions/domain.NodeMeta"}, "name": {"type": "string"}, "node_id": {"type": "string"}, "release_id": {"description": "release", "type": "string"}, "release_message": {"type": "string"}, "release_name": {"type": "string"}, "updated_at": {"type": "string"}}}, "domain.NodeStatus": {"type": "integer", "enum": [1, 2], "x-enum-varnames": ["NodeStatusDraft", "NodeStatusReleased"]}, "domain.NodeSummaryReq": {"type": "object", "required": ["ids", "kb_id"], "properties": {"ids": {"type": "array", "items": {"type": "string"}}, "kb_id": {"type": "string"}}}, "domain.NodeType": {"type": "integer", "enum": [1, 2], "x-enum-varnames": ["NodeTypeFolder", "NodeTypeDocument"]}, "domain.NodeVisibility": {"type": "integer", "enum": [1, 2], "x-enum-varnames": ["NodeVisibilityPrivate", "NodeVisibilityPublic"]}, "domain.NotnionGetListReq": {"type": "object", "properties": {"cation_title": {"type": "string"}, "integration": {"type": "string"}}}, "domain.ObjectUploadResp": {"type": "object", "properties": {"key": {"type": "string"}}}, "domain.Page": {"type": "object", "properties": {"content": {"type": "string"}, "id": {"type": "string"}, "parent_id": {"type": "string"}, "title": {"type": "string"}}}, "domain.PageInfo": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}}}, "domain.PaginatedResult-array_domain_ConversationMessageListItem": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/domain.ConversationMessageListItem"}}, "total": {"type": "integer"}}}, "domain.ParseURLItem": {"type": "object", "properties": {"desc": {"type": "string"}, "published": {"type": "string"}, "title": {"type": "string"}, "url": {"type": "string"}}}, "domain.ParseURLReq": {"type": "object", "required": ["url"], "properties": {"url": {"type": "string"}}}, "domain.ParseURLResp": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/domain.ParseURLItem"}}}}, "domain.ProviderModelListItem": {"type": "object", "properties": {"model": {"type": "string"}}}, "domain.RecommendNodeListResp": {"type": "object", "properties": {"emoji": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "parent_id": {"type": "string"}, "position": {"type": "number"}, "recommend_nodes": {"type": "array", "items": {"$ref": "#/definitions/domain.RecommendNodeListResp"}}, "summary": {"type": "string"}, "type": {"$ref": "#/definitions/domain.NodeType"}}}, "domain.ResetPasswordReq": {"type": "object", "required": ["id", "new_password"], "properties": {"id": {"type": "string"}, "new_password": {"type": "string", "minLength": 8}}}, "domain.Response": {"type": "object", "properties": {"data": {}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "domain.ScoreType": {"type": "integer", "enum": [1, -1], "x-enum-varnames": ["Like", "DisLike"]}, "domain.ScrapeReq": {"type": "object", "required": ["kb_id"], "properties": {"kb_id": {"type": "string"}, "url": {"type": "string"}}}, "domain.ScrapeResp": {"type": "object", "properties": {"content": {"type": "string"}, "title": {"type": "string"}}}, "domain.SearchDocxReq": {"type": "object", "properties": {"app_id": {"type": "string"}, "app_secret": {"type": "string"}, "user_access_token": {"type": "string"}}}, "domain.SearchDocxResp": {"type": "object", "properties": {"name": {"type": "string"}, "obj_token": {"type": "string"}, "obj_type": {"type": "integer"}, "url": {"type": "string"}}}, "domain.SearchWikiReq": {"type": "object", "properties": {"app_id": {"type": "string"}, "app_secret": {"type": "string"}, "query": {"type": "string"}, "space_id": {"type": "string"}, "user_access_token": {"type": "string"}}}, "domain.SearchWikiResp": {"type": "object", "properties": {"obj_token": {"type": "string"}, "obj_type": {"type": "integer"}, "space_id": {"type": "string"}, "title": {"type": "string"}, "url": {"type": "string"}}}, "domain.ShareCommentListItem": {"type": "object", "properties": {"content": {"type": "string"}, "created_at": {"type": "string"}, "id": {"type": "string"}, "info": {"$ref": "#/definitions/domain.CommentInfo"}, "ip_address": {"description": "ip地址", "allOf": [{"$ref": "#/definitions/domain.IPAddress"}]}, "kb_id": {"type": "string"}, "node_id": {"type": "string"}, "parent_id": {"type": "string"}, "root_id": {"type": "string"}}}, "domain.SimpleAuth": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "password": {"type": "string"}}}, "domain.Source": {"type": "object", "properties": {"obj_token": {"type": "string"}, "obj_type": {"type": "integer"}, "url": {"type": "string"}}}, "domain.StatPageReq": {"type": "object", "required": ["scene"], "properties": {"node_id": {"type": "string"}, "scene": {"enum": [1, 2, 3, 4], "allOf": [{"$ref": "#/definitions/domain.StatPageScene"}]}}}, "domain.StatPageScene": {"type": "integer", "enum": [1, 2, 3, 4], "x-enum-varnames": ["StatPageSceneWelcome", "StatPageSceneNodeDetail", "StatPageSceneChat", "StatPageSceneLogin"]}, "domain.TextReq": {"type": "object", "required": ["text"], "properties": {"action": {"description": "action: improve, summary, extend, shorten, etc.", "type": "string"}, "text": {"type": "string"}}}, "domain.ThemeAndStyle": {"type": "object", "properties": {"bg_image": {"type": "string"}}}, "domain.UpdateAppReq": {"type": "object", "properties": {"name": {"type": "string"}, "settings": {"$ref": "#/definitions/domain.AppSettings"}}}, "domain.UpdateKnowledgeBaseReq": {"type": "object", "required": ["id"], "properties": {"access_settings": {"$ref": "#/definitions/domain.AccessSettings"}, "id": {"type": "string"}, "name": {"type": "string"}}}, "domain.UpdateModelReq": {"type": "object", "required": ["base_url", "id", "model", "provider", "type"], "properties": {"api_header": {"type": "string"}, "api_key": {"type": "string"}, "api_version": {"description": "for azure openai", "type": "string"}, "base_url": {"type": "string"}, "id": {"type": "string"}, "model": {"type": "string"}, "provider": {"enum": ["OpenAI", "Ollama", "DeepSeek", "SiliconFlow", "Moonshot", "Other", "AzureOpenAI", "BaiZhiCloud", "Hunyuan", "<PERSON><PERSON><PERSON>", "Volcengine", "Gemini"], "allOf": [{"$ref": "#/definitions/domain.ModelProvider"}]}, "type": {"enum": ["chat", "embedding", "rerank"], "allOf": [{"$ref": "#/definitions/domain.ModelType"}]}}}, "domain.UpdateNodeReq": {"type": "object", "required": ["id", "kb_id"], "properties": {"content": {"type": "string"}, "emoji": {"type": "string"}, "id": {"type": "string"}, "kb_id": {"type": "string"}, "name": {"type": "string"}, "summary": {"type": "string"}, "visibility": {"$ref": "#/definitions/domain.NodeVisibility"}}}, "domain.UserInfo": {"type": "object", "properties": {"avatar": {"description": "avatar", "type": "string"}, "email": {"type": "string"}, "from": {"$ref": "#/definitions/domain.MessageFrom"}, "name": {"type": "string"}, "real_name": {"type": "string"}, "user_id": {"type": "string"}}}, "domain.UserInfoResp": {"type": "object", "properties": {"account": {"type": "string"}, "created_at": {"type": "string"}, "id": {"type": "string"}, "last_access": {"type": "string"}}}, "domain.UserListItemResp": {"type": "object", "properties": {"account": {"type": "string"}, "id": {"type": "string"}, "last_access": {"type": "string"}}}, "domain.WebAppCommentSettings": {"type": "object", "properties": {"is_enable": {"type": "boolean"}, "moderation_enable": {"type": "boolean"}}}, "domain.WidgetBotSettings": {"type": "object", "properties": {"btn_logo": {"type": "string"}, "btn_text": {"type": "string"}, "is_open": {"type": "boolean"}, "theme_mode": {"type": "string"}}}, "domain.WikiJSResp": {"type": "object", "properties": {"content": {"type": "string"}, "id": {"type": "integer"}, "title": {"type": "string"}}}, "schema.RoleType": {"type": "string", "enum": ["assistant", "user", "system", "tool"], "x-enum-varnames": ["Assistant", "User", "System", "Tool"]}, "share.ShareCommentLists": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/domain.ShareCommentListItem"}}, "total": {"type": "integer"}}}, "v1.CommentLists": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/domain.CommentListItem"}}, "total": {"type": "integer"}}}, "v1.ConversationListItems": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/domain.ConversationListItem"}}, "total": {"type": "integer"}}}}}