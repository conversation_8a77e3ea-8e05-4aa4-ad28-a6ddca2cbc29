definitions:
  domain.AccessSettings:
    properties:
      base_url:
        type: string
      hosts:
        items:
          type: string
        type: array
      ports:
        items:
          type: integer
        type: array
      private_key:
        type: string
      public_key:
        type: string
      simple_auth:
        $ref: '#/definitions/domain.SimpleAuth'
      ssl_ports:
        items:
          type: integer
        type: array
      trusted_proxies:
        items:
          type: string
        type: array
    type: object
  domain.AnalysisConfluenceResp:
    properties:
      content:
        type: string
      id:
        type: string
      title:
        type: string
    type: object
  domain.AppDetailResp:
    properties:
      id:
        type: string
      kb_id:
        type: string
      name:
        type: string
      recommend_nodes:
        items:
          $ref: '#/definitions/domain.RecommendNodeListResp'
        type: array
      settings:
        $ref: '#/definitions/domain.AppSettingsResp'
      type:
        $ref: '#/definitions/domain.AppType'
    type: object
  domain.AppSettings:
    properties:
      auto_sitemap:
        type: boolean
      body_code:
        type: string
      btns:
        items: {}
        type: array
      catalog_settings:
        allOf:
        - $ref: '#/definitions/domain.CatalogSettings'
        description: catalog settings
      desc:
        description: seo
        type: string
      dingtalk_bot_client_id:
        type: string
      dingtalk_bot_client_secret:
        type: string
      dingtalk_bot_is_enabled:
        description: DingTalkBot
        type: boolean
      dingtalk_bot_template_id:
        type: string
      discord_bot_is_enabled:
        description: DisCordBot
        type: boolean
      discord_bot_token:
        type: string
      feishu_bot_app_id:
        type: string
      feishu_bot_app_secret:
        type: string
      feishu_bot_is_enabled:
        description: FeishuBot
        type: boolean
      footer_settings:
        allOf:
        - $ref: '#/definitions/domain.FooterSettings'
        description: footer settings
      head_code:
        description: inject code
        type: string
      icon:
        type: string
      keyword:
        type: string
      recommend_node_ids:
        items:
          type: string
        type: array
      recommend_questions:
        items:
          type: string
        type: array
      search_placeholder:
        type: string
      theme_and_style:
        $ref: '#/definitions/domain.ThemeAndStyle'
      theme_mode:
        description: theme
        type: string
      title:
        description: nav
        type: string
      web_app_comment_settings:
        allOf:
        - $ref: '#/definitions/domain.WebAppCommentSettings'
        description: webapp comment settings
      wechat_app_agent_id:
        type: string
      wechat_app_corpid:
        type: string
      wechat_app_encodingaeskey:
        type: string
      wechat_app_is_enabled:
        description: WechatAppBot
        type: boolean
      wechat_app_secret:
        type: string
      wechat_app_token:
        type: string
      wechat_official_account_app_id:
        type: string
      wechat_official_account_app_secret:
        type: string
      wechat_official_account_encodingaeskey:
        type: string
      wechat_official_account_is_enabled:
        description: WechatOfficialAccount
        type: boolean
      wechat_official_account_token:
        type: string
      wechat_service_corpid:
        type: string
      wechat_service_encodingaeskey:
        type: string
      wechat_service_is_enabled:
        description: WechatServiceBot
        type: boolean
      wechat_service_secret:
        type: string
      wechat_service_token:
        type: string
      welcome_str:
        description: welcome
        type: string
      widget_bot_settings:
        allOf:
        - $ref: '#/definitions/domain.WidgetBotSettings'
        description: Widget bot settings
    type: object
  domain.AppSettingsResp:
    properties:
      auto_sitemap:
        type: boolean
      body_code:
        type: string
      btns:
        items: {}
        type: array
      catalog_settings:
        allOf:
        - $ref: '#/definitions/domain.CatalogSettings'
        description: catalog settings
      desc:
        description: seo
        type: string
      dingtalk_bot_client_id:
        type: string
      dingtalk_bot_client_secret:
        type: string
      dingtalk_bot_is_enabled:
        description: DingTalkBot
        type: boolean
      dingtalk_bot_template_id:
        type: string
      discord_bot_is_enabled:
        description: DisCordBot
        type: boolean
      discord_bot_token:
        type: string
      feishu_bot_app_id:
        type: string
      feishu_bot_app_secret:
        type: string
      feishu_bot_is_enabled:
        description: FeishuBot
        type: boolean
      footer_settings:
        allOf:
        - $ref: '#/definitions/domain.FooterSettings'
        description: footer settings
      head_code:
        description: inject code
        type: string
      icon:
        type: string
      keyword:
        type: string
      recommend_node_ids:
        items:
          type: string
        type: array
      recommend_questions:
        items:
          type: string
        type: array
      search_placeholder:
        type: string
      theme_and_style:
        $ref: '#/definitions/domain.ThemeAndStyle'
      theme_mode:
        description: theme
        type: string
      title:
        description: nav
        type: string
      web_app_comment_settings:
        allOf:
        - $ref: '#/definitions/domain.WebAppCommentSettings'
        description: webapp comment settings
      wechat_app_agent_id:
        type: string
      wechat_app_corpid:
        type: string
      wechat_app_encodingaeskey:
        type: string
      wechat_app_is_enabled:
        description: WechatAppBot
        type: boolean
      wechat_app_secret:
        type: string
      wechat_app_token:
        type: string
      wechat_official_account_app_id:
        type: string
      wechat_official_account_app_secret:
        type: string
      wechat_official_account_encodingaeskey:
        type: string
      wechat_official_account_is_enabled:
        description: WechatOfficialAccount
        type: boolean
      wechat_official_account_token:
        type: string
      wechat_service_corpid:
        type: string
      wechat_service_encodingaeskey:
        type: string
      wechat_service_is_enabled:
        description: WechatServiceBot
        type: boolean
      wechat_service_secret:
        type: string
      wechat_service_token:
        type: string
      welcome_str:
        description: welcome
        type: string
      widget_bot_settings:
        allOf:
        - $ref: '#/definitions/domain.WidgetBotSettings'
        description: WidgetBot
    type: object
  domain.AppType:
    enum:
    - 1
    - 2
    - 3
    - 4
    - 5
    - 6
    - 7
    - 8
    type: integer
    x-enum-varnames:
    - AppTypeWeb
    - AppTypeWidget
    - AppTypeDingTalkBot
    - AppTypeFeishuBot
    - AppTypeWechatBot
    - AppTypeWechatServiceBot
    - AppTypeDisCordBot
    - AppTypeWechatOfficialAccount
  domain.BatchMoveReq:
    properties:
      ids:
        items:
          type: string
        type: array
      kb_id:
        type: string
      parent_id:
        type: string
    required:
    - ids
    - kb_id
    type: object
  domain.BrandGroup:
    properties:
      links:
        items:
          $ref: '#/definitions/domain.Link'
        type: array
      name:
        type: string
    type: object
  domain.CatalogSettings:
    properties:
      catalog_folder:
        description: '1: 展开, 2: 折叠, default: 1'
        type: integer
      catalog_visible:
        description: '1: 显示, 2: 隐藏, default: 1'
        type: integer
      catalog_width:
        description: '200 - 300, default: 260'
        type: integer
    type: object
  domain.ChatRequest:
    properties:
      app_type:
        allOf:
        - $ref: '#/definitions/domain.AppType'
        enum:
        - 1
        - 2
      conversation_id:
        type: string
      message:
        type: string
      nonce:
        type: string
    required:
    - app_type
    - message
    type: object
  domain.CheckModelReq:
    properties:
      api_header:
        type: string
      api_key:
        type: string
      api_version:
        description: for azure openai
        type: string
      base_url:
        type: string
      model:
        type: string
      provider:
        allOf:
        - $ref: '#/definitions/domain.ModelProvider'
        enum:
        - OpenAI
        - Ollama
        - DeepSeek
        - SiliconFlow
        - Moonshot
        - Other
        - AzureOpenAI
        - BaiZhiCloud
        - Hunyuan
        - BaiLian
        - Volcengine
        - Gemini
      type:
        allOf:
        - $ref: '#/definitions/domain.ModelType'
        enum:
        - chat
        - embedding
        - rerank
    required:
    - base_url
    - model
    - provider
    - type
    type: object
  domain.CheckModelResp:
    properties:
      content:
        type: string
      error:
        type: string
    type: object
  domain.CommentInfo:
    properties:
      remote_ip:
        type: string
      user_name:
        type: string
    type: object
  domain.CommentListItem:
    properties:
      content:
        type: string
      created_at:
        type: string
      id:
        type: string
      info:
        $ref: '#/definitions/domain.CommentInfo'
      ip_address:
        allOf:
        - $ref: '#/definitions/domain.IPAddress'
        description: ip地址
      node_id:
        type: string
      node_name:
        description: 文档标题
        type: string
      node_type:
        type: integer
      root_id:
        type: string
    type: object
  domain.CommentReq:
    properties:
      content:
        type: string
      node_id:
        type: string
      parent_id:
        type: string
      root_id:
        type: string
      user_name:
        type: string
    required:
    - content
    - node_id
    type: object
  domain.ConversationDetailResp:
    properties:
      app_id:
        type: string
      created_at:
        type: string
      id:
        type: string
      ip_address:
        $ref: '#/definitions/domain.IPAddress'
      messages:
        items:
          $ref: '#/definitions/domain.ConversationMessage'
        type: array
      references:
        items:
          $ref: '#/definitions/domain.ConversationReference'
        type: array
      remote_ip:
        type: string
      subject:
        type: string
    type: object
  domain.ConversationInfo:
    properties:
      user_info:
        $ref: '#/definitions/domain.UserInfo'
    type: object
  domain.ConversationListItem:
    properties:
      app_name:
        type: string
      app_type:
        $ref: '#/definitions/domain.AppType'
      created_at:
        type: string
      feedback_info:
        allOf:
        - $ref: '#/definitions/domain.FeedBackInfo'
        description: 用户反馈信息
      id:
        type: string
      info:
        allOf:
        - $ref: '#/definitions/domain.ConversationInfo'
        description: 用户信息
      ip_address:
        $ref: '#/definitions/domain.IPAddress'
      remote_ip:
        type: string
      subject:
        type: string
    type: object
  domain.ConversationMessage:
    properties:
      app_id:
        type: string
      completion_tokens:
        type: integer
      content:
        type: string
      conversation_id:
        type: string
      created_at:
        type: string
      id:
        type: string
      info:
        allOf:
        - $ref: '#/definitions/domain.FeedBackInfo'
        description: feedbackinfo
      kb_id:
        type: string
      model:
        type: string
      parent_id:
        description: parent_id
        type: string
      prompt_tokens:
        type: integer
      provider:
        allOf:
        - $ref: '#/definitions/domain.ModelProvider'
        description: model
      remote_ip:
        description: stats
        type: string
      role:
        $ref: '#/definitions/schema.RoleType'
      total_tokens:
        type: integer
    type: object
  domain.ConversationMessageListItem:
    properties:
      app_id:
        type: string
      app_type:
        $ref: '#/definitions/domain.AppType'
      conversation_id:
        type: string
      conversation_info:
        allOf:
        - $ref: '#/definitions/domain.ConversationInfo'
        description: userInfo
      created_at:
        type: string
      id:
        type: string
      info:
        allOf:
        - $ref: '#/definitions/domain.FeedBackInfo'
        description: feedbackInfo
      ip_address:
        $ref: '#/definitions/domain.IPAddress'
      question:
        type: string
      remote_ip:
        description: stats
        type: string
    type: object
  domain.ConversationReference:
    properties:
      app_id:
        type: string
      conversation_id:
        type: string
      name:
        type: string
      node_id:
        type: string
      url:
        type: string
    type: object
  domain.CreateKBReleaseReq:
    properties:
      kb_id:
        type: string
      message:
        type: string
      node_ids:
        description: create release after these nodes published
        items:
          type: string
        type: array
      tag:
        type: string
    required:
    - kb_id
    - message
    - tag
    type: object
  domain.CreateKnowledgeBaseReq:
    properties:
      hosts:
        items:
          type: string
        type: array
      name:
        type: string
      ports:
        items:
          type: integer
        type: array
      private_key:
        type: string
      public_key:
        type: string
      ssl_ports:
        items:
          type: integer
        type: array
    required:
    - name
    type: object
  domain.CreateModelReq:
    properties:
      api_header:
        type: string
      api_key:
        type: string
      api_version:
        description: for azure openai
        type: string
      base_url:
        type: string
      model:
        type: string
      provider:
        allOf:
        - $ref: '#/definitions/domain.ModelProvider'
        enum:
        - OpenAI
        - Ollama
        - DeepSeek
        - SiliconFlow
        - Moonshot
        - Other
        - AzureOpenAI
        - BaiZhiCloud
        - Hunyuan
        - BaiLian
        - Volcengine
        - Gemini
      type:
        allOf:
        - $ref: '#/definitions/domain.ModelType'
        enum:
        - chat
        - embedding
        - rerank
    required:
    - base_url
    - model
    - provider
    - type
    type: object
  domain.CreateNodeReq:
    properties:
      content:
        type: string
      emoji:
        type: string
      kb_id:
        type: string
      name:
        type: string
      parent_id:
        type: string
      type:
        allOf:
        - $ref: '#/definitions/domain.NodeType'
        enum:
        - 1
        - 2
      visibility:
        $ref: '#/definitions/domain.NodeVisibility'
    required:
    - kb_id
    - name
    - type
    type: object
  domain.CreateUserReq:
    properties:
      account:
        type: string
      password:
        minLength: 8
        type: string
    required:
    - account
    - password
    type: object
  domain.DeleteUserReq:
    properties:
      user_id:
        type: string
    required:
    - user_id
    type: object
  domain.EpubResp:
    properties:
      content:
        type: string
      title:
        type: string
    type: object
  domain.FeedBackInfo:
    properties:
      feedback_content:
        type: string
      feedback_type:
        $ref: '#/definitions/domain.FeedbackType'
      score:
        $ref: '#/definitions/domain.ScoreType'
    type: object
  domain.FeedbackRequest:
    properties:
      conversation_id:
        type: string
      feedback_content:
        description: 限制内容长度
        maxLength: 200
        type: string
      message_id:
        type: string
      score:
        allOf:
        - $ref: '#/definitions/domain.ScoreType'
        description: -1 踩 ,0 1 赞成
      type:
        allOf:
        - $ref: '#/definitions/domain.FeedbackType'
        description: 1 内容不准确，2 没有帮助，3 其他
    required:
    - message_id
    type: object
  domain.FeedbackType:
    enum:
    - 1
    - 2
    - 3
    type: integer
    x-enum-varnames:
    - ContentErr
    - NoHelp
    - Other
  domain.FooterSettings:
    properties:
      brand_desc:
        type: string
      brand_groups:
        items:
          $ref: '#/definitions/domain.BrandGroup'
        type: array
      brand_logo:
        type: string
      brand_name:
        type: string
      corp_name:
        type: string
      footer_style:
        type: string
      icp:
        type: string
    type: object
  domain.GetDocsReq:
    properties:
      integration:
        type: string
      kb_id:
        type: string
      pages:
        items:
          $ref: '#/definitions/domain.PageInfo'
        type: array
    required:
    - kb_id
    type: object
  domain.GetDocxReq:
    properties:
      app_id:
        type: string
      app_secret:
        type: string
      kb_id:
        type: string
      sources:
        items:
          $ref: '#/definitions/domain.Source'
        type: array
      user_access_token:
        type: string
    required:
    - kb_id
    - sources
    type: object
  domain.GetDocxResp:
    properties:
      content:
        type: string
      title:
        type: string
    type: object
  domain.GetKBReleaseListResp:
    properties:
      data:
        items:
          $ref: '#/definitions/domain.KBReleaseListItemResp'
        type: array
      total:
        type: integer
    type: object
  domain.GetNodeReleaseDetailResp:
    properties:
      content:
        type: string
      meta:
        $ref: '#/definitions/domain.NodeMeta'
      name:
        type: string
    type: object
  domain.GetProviderModelListResp:
    properties:
      models:
        items:
          $ref: '#/definitions/domain.ProviderModelListItem'
        type: array
    type: object
  domain.GetSpaceListReq:
    properties:
      app_id:
        type: string
      app_secret:
        type: string
      user_access_token:
        type: string
    type: object
  domain.GetSpaceListResp:
    properties:
      name:
        type: string
      space_id:
        type: string
    type: object
  domain.IPAddress:
    properties:
      city:
        type: string
      country:
        type: string
      ip:
        type: string
      province:
        type: string
    type: object
  domain.KBReleaseListItemResp:
    properties:
      created_at:
        type: string
      id:
        type: string
      kb_id:
        type: string
      message:
        type: string
      tag:
        type: string
    type: object
  domain.KnowledgeBaseDetail:
    properties:
      access_settings:
        $ref: '#/definitions/domain.AccessSettings'
      created_at:
        type: string
      dataset_id:
        type: string
      id:
        type: string
      name:
        type: string
      updated_at:
        type: string
    type: object
  domain.KnowledgeBaseListItem:
    properties:
      access_settings:
        $ref: '#/definitions/domain.AccessSettings'
      created_at:
        type: string
      dataset_id:
        type: string
      id:
        type: string
      name:
        type: string
      updated_at:
        type: string
    type: object
  domain.Link:
    properties:
      name:
        type: string
      url:
        type: string
    type: object
  domain.LoginReq:
    properties:
      account:
        type: string
      password:
        type: string
    required:
    - account
    - password
    type: object
  domain.LoginResp:
    properties:
      token:
        type: string
    type: object
  domain.MessageFrom:
    enum:
    - 1
    - 2
    type: integer
    x-enum-varnames:
    - MessageFromGroup
    - MessageFromPrivate
  domain.ModelDetailResp:
    properties:
      api_header:
        type: string
      api_key:
        type: string
      api_version:
        description: for azure openai
        type: string
      base_url:
        type: string
      completion_tokens:
        type: integer
      created_at:
        type: string
      id:
        type: string
      model:
        type: string
      prompt_tokens:
        type: integer
      provider:
        $ref: '#/definitions/domain.ModelProvider'
      total_tokens:
        type: integer
      type:
        $ref: '#/definitions/domain.ModelType'
      updated_at:
        type: string
    type: object
  domain.ModelListItem:
    properties:
      api_header:
        type: string
      api_key:
        type: string
      api_version:
        description: for azure openai
        type: string
      base_url:
        type: string
      completion_tokens:
        type: integer
      id:
        type: string
      model:
        type: string
      prompt_tokens:
        type: integer
      provider:
        $ref: '#/definitions/domain.ModelProvider'
      total_tokens:
        type: integer
      type:
        $ref: '#/definitions/domain.ModelType'
    type: object
  domain.ModelProvider:
    enum:
    - OpenAI
    - Ollama
    - DeepSeek
    - Moonshot
    - SiliconFlow
    - AzureOpenAI
    - BaiZhiCloud
    - Hunyuan
    - BaiLian
    - Volcengine
    - Gemini
    - Other
    type: string
    x-enum-varnames:
    - ModelProviderBrandOpenAI
    - ModelProviderBrandOllama
    - ModelProviderBrandDeepSeek
    - ModelProviderBrandMoonshot
    - ModelProviderBrandSiliconFlow
    - ModelProviderBrandAzureOpenAI
    - ModelProviderBrandBaiZhiCloud
    - ModelProviderBrandHunyuan
    - ModelProviderBrandBaiLian
    - ModelProviderBrandVolcengine
    - ModelProviderBrandGemini
    - ModelProviderBrandOther
  domain.ModelType:
    enum:
    - chat
    - embedding
    - rerank
    type: string
    x-enum-varnames:
    - ModelTypeChat
    - ModelTypeEmbedding
    - ModelTypeRerank
  domain.MoveNodeReq:
    properties:
      id:
        type: string
      next_id:
        type: string
      parent_id:
        type: string
      prev_id:
        type: string
    required:
    - id
    type: object
  domain.NodeActionReq:
    properties:
      action:
        enum:
        - delete
        - private
        - public
        type: string
      ids:
        items:
          type: string
        type: array
      kb_id:
        type: string
    required:
    - action
    - ids
    - kb_id
    type: object
  domain.NodeDetailResp:
    properties:
      content:
        type: string
      created_at:
        type: string
      id:
        type: string
      kb_id:
        type: string
      meta:
        $ref: '#/definitions/domain.NodeMeta'
      name:
        type: string
      parent_id:
        type: string
      status:
        $ref: '#/definitions/domain.NodeStatus'
      type:
        $ref: '#/definitions/domain.NodeType'
      updated_at:
        type: string
      visibility:
        $ref: '#/definitions/domain.NodeVisibility'
    type: object
  domain.NodeListItemResp:
    properties:
      created_at:
        type: string
      emoji:
        type: string
      id:
        type: string
      name:
        type: string
      parent_id:
        type: string
      position:
        type: number
      status:
        $ref: '#/definitions/domain.NodeStatus'
      summary:
        type: string
      type:
        $ref: '#/definitions/domain.NodeType'
      updated_at:
        type: string
      visibility:
        $ref: '#/definitions/domain.NodeVisibility'
    type: object
  domain.NodeMeta:
    properties:
      emoji:
        type: string
      summary:
        type: string
    type: object
  domain.NodeReleaseListItem:
    properties:
      id:
        type: string
      meta:
        $ref: '#/definitions/domain.NodeMeta'
      name:
        type: string
      node_id:
        type: string
      release_id:
        description: release
        type: string
      release_message:
        type: string
      release_name:
        type: string
      updated_at:
        type: string
    type: object
  domain.NodeStatus:
    enum:
    - 1
    - 2
    type: integer
    x-enum-varnames:
    - NodeStatusDraft
    - NodeStatusReleased
  domain.NodeSummaryReq:
    properties:
      ids:
        items:
          type: string
        type: array
      kb_id:
        type: string
    required:
    - ids
    - kb_id
    type: object
  domain.NodeType:
    enum:
    - 1
    - 2
    type: integer
    x-enum-varnames:
    - NodeTypeFolder
    - NodeTypeDocument
  domain.NodeVisibility:
    enum:
    - 1
    - 2
    type: integer
    x-enum-varnames:
    - NodeVisibilityPrivate
    - NodeVisibilityPublic
  domain.NotnionGetListReq:
    properties:
      cation_title:
        type: string
      integration:
        type: string
    type: object
  domain.ObjectUploadResp:
    properties:
      key:
        type: string
    type: object
  domain.Page:
    properties:
      content:
        type: string
      id:
        type: string
      parent_id:
        type: string
      title:
        type: string
    type: object
  domain.PageInfo:
    properties:
      id:
        type: string
      title:
        type: string
    type: object
  domain.PaginatedResult-array_domain_ConversationMessageListItem:
    properties:
      data:
        items:
          $ref: '#/definitions/domain.ConversationMessageListItem'
        type: array
      total:
        type: integer
    type: object
  domain.ParseURLItem:
    properties:
      desc:
        type: string
      published:
        type: string
      title:
        type: string
      url:
        type: string
    type: object
  domain.ParseURLReq:
    properties:
      url:
        type: string
    required:
    - url
    type: object
  domain.ParseURLResp:
    properties:
      items:
        items:
          $ref: '#/definitions/domain.ParseURLItem'
        type: array
    type: object
  domain.ProviderModelListItem:
    properties:
      model:
        type: string
    type: object
  domain.RecommendNodeListResp:
    properties:
      emoji:
        type: string
      id:
        type: string
      name:
        type: string
      parent_id:
        type: string
      position:
        type: number
      recommend_nodes:
        items:
          $ref: '#/definitions/domain.RecommendNodeListResp'
        type: array
      summary:
        type: string
      type:
        $ref: '#/definitions/domain.NodeType'
    type: object
  domain.ResetPasswordReq:
    properties:
      id:
        type: string
      new_password:
        minLength: 8
        type: string
    required:
    - id
    - new_password
    type: object
  domain.Response:
    properties:
      data: {}
      message:
        type: string
      success:
        type: boolean
    type: object
  domain.ScoreType:
    enum:
    - 1
    - -1
    type: integer
    x-enum-varnames:
    - Like
    - DisLike
  domain.ScrapeReq:
    properties:
      kb_id:
        type: string
      url:
        type: string
    required:
    - kb_id
    type: object
  domain.ScrapeResp:
    properties:
      content:
        type: string
      title:
        type: string
    type: object
  domain.SearchDocxReq:
    properties:
      app_id:
        type: string
      app_secret:
        type: string
      user_access_token:
        type: string
    type: object
  domain.SearchDocxResp:
    properties:
      name:
        type: string
      obj_token:
        type: string
      obj_type:
        type: integer
      url:
        type: string
    type: object
  domain.SearchWikiReq:
    properties:
      app_id:
        type: string
      app_secret:
        type: string
      query:
        type: string
      space_id:
        type: string
      user_access_token:
        type: string
    type: object
  domain.SearchWikiResp:
    properties:
      obj_token:
        type: string
      obj_type:
        type: integer
      space_id:
        type: string
      title:
        type: string
      url:
        type: string
    type: object
  domain.ShareCommentListItem:
    properties:
      content:
        type: string
      created_at:
        type: string
      id:
        type: string
      info:
        $ref: '#/definitions/domain.CommentInfo'
      ip_address:
        allOf:
        - $ref: '#/definitions/domain.IPAddress'
        description: ip地址
      kb_id:
        type: string
      node_id:
        type: string
      parent_id:
        type: string
      root_id:
        type: string
    type: object
  domain.SimpleAuth:
    properties:
      enabled:
        type: boolean
      password:
        type: string
    type: object
  domain.Source:
    properties:
      obj_token:
        type: string
      obj_type:
        type: integer
      url:
        type: string
    type: object
  domain.StatPageReq:
    properties:
      node_id:
        type: string
      scene:
        allOf:
        - $ref: '#/definitions/domain.StatPageScene'
        enum:
        - 1
        - 2
        - 3
        - 4
    required:
    - scene
    type: object
  domain.StatPageScene:
    enum:
    - 1
    - 2
    - 3
    - 4
    type: integer
    x-enum-varnames:
    - StatPageSceneWelcome
    - StatPageSceneNodeDetail
    - StatPageSceneChat
    - StatPageSceneLogin
  domain.TextReq:
    properties:
      action:
        description: 'action: improve, summary, extend, shorten, etc.'
        type: string
      text:
        type: string
    required:
    - text
    type: object
  domain.ThemeAndStyle:
    properties:
      bg_image:
        type: string
    type: object
  domain.UpdateAppReq:
    properties:
      name:
        type: string
      settings:
        $ref: '#/definitions/domain.AppSettings'
    type: object
  domain.UpdateKnowledgeBaseReq:
    properties:
      access_settings:
        $ref: '#/definitions/domain.AccessSettings'
      id:
        type: string
      name:
        type: string
    required:
    - id
    type: object
  domain.UpdateModelReq:
    properties:
      api_header:
        type: string
      api_key:
        type: string
      api_version:
        description: for azure openai
        type: string
      base_url:
        type: string
      id:
        type: string
      model:
        type: string
      provider:
        allOf:
        - $ref: '#/definitions/domain.ModelProvider'
        enum:
        - OpenAI
        - Ollama
        - DeepSeek
        - SiliconFlow
        - Moonshot
        - Other
        - AzureOpenAI
        - BaiZhiCloud
        - Hunyuan
        - BaiLian
        - Volcengine
        - Gemini
      type:
        allOf:
        - $ref: '#/definitions/domain.ModelType'
        enum:
        - chat
        - embedding
        - rerank
    required:
    - base_url
    - id
    - model
    - provider
    - type
    type: object
  domain.UpdateNodeReq:
    properties:
      content:
        type: string
      emoji:
        type: string
      id:
        type: string
      kb_id:
        type: string
      name:
        type: string
      summary:
        type: string
      visibility:
        $ref: '#/definitions/domain.NodeVisibility'
    required:
    - id
    - kb_id
    type: object
  domain.UserInfo:
    properties:
      avatar:
        description: avatar
        type: string
      email:
        type: string
      from:
        $ref: '#/definitions/domain.MessageFrom'
      name:
        type: string
      real_name:
        type: string
      user_id:
        type: string
    type: object
  domain.UserInfoResp:
    properties:
      account:
        type: string
      created_at:
        type: string
      id:
        type: string
      last_access:
        type: string
    type: object
  domain.UserListItemResp:
    properties:
      account:
        type: string
      id:
        type: string
      last_access:
        type: string
    type: object
  domain.WebAppCommentSettings:
    properties:
      is_enable:
        type: boolean
      moderation_enable:
        type: boolean
    type: object
  domain.WidgetBotSettings:
    properties:
      btn_logo:
        type: string
      btn_text:
        type: string
      is_open:
        type: boolean
      theme_mode:
        type: string
    type: object
  domain.WikiJSResp:
    properties:
      content:
        type: string
      id:
        type: integer
      title:
        type: string
    type: object
  schema.RoleType:
    enum:
    - assistant
    - user
    - system
    - tool
    type: string
    x-enum-varnames:
    - Assistant
    - User
    - System
    - Tool
  share.ShareCommentLists:
    properties:
      data:
        items:
          $ref: '#/definitions/domain.ShareCommentListItem'
        type: array
      total:
        type: integer
    type: object
  v1.CommentLists:
    properties:
      data:
        items:
          $ref: '#/definitions/domain.CommentListItem'
        type: array
      total:
        type: integer
    type: object
  v1.ConversationListItems:
    properties:
      data:
        items:
          $ref: '#/definitions/domain.ConversationListItem'
        type: array
      total:
        type: integer
    type: object
info:
  contact: {}
paths:
  /api/v1/app:
    delete:
      consumes:
      - application/json
      description: Delete app
      parameters:
      - description: app id
        in: query
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: Delete app
      tags:
      - app
    put:
      consumes:
      - application/json
      description: Update app
      parameters:
      - description: app
        in: body
        name: app
        required: true
        schema:
          $ref: '#/definitions/domain.UpdateAppReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: Update app
      tags:
      - app
  /api/v1/app/detail:
    get:
      consumes:
      - application/json
      description: Get app detail
      parameters:
      - description: kb id
        in: query
        name: kb_id
        required: true
        type: string
      - description: app type
        in: query
        name: type
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  $ref: '#/definitions/domain.AppDetailResp'
              type: object
      summary: Get app detail
      tags:
      - app
  /api/v1/comment:
    get:
      consumes:
      - application/json
      description: GetCommentList
      parameters:
      - in: query
        name: kb_id
        required: true
        type: string
      - in: query
        minimum: 1
        name: page
        required: true
        type: integer
      - in: query
        minimum: 1
        name: per_page
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: conversationList
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  $ref: '#/definitions/v1.CommentLists'
              type: object
      summary: GetCommentList
      tags:
      - comment
  /api/v1/comment/list:
    delete:
      consumes:
      - application/json
      description: DeleteCommentList
      parameters:
      - collectionFormat: csv
        in: query
        items:
          type: string
        name: ids
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: total
          schema:
            $ref: '#/definitions/domain.Response'
      summary: DeleteCommentList
      tags:
      - comment
  /api/v1/conversation:
    get:
      consumes:
      - application/json
      description: get conversation list
      parameters:
      - in: query
        name: app_id
        type: string
      - in: query
        name: kb_id
        required: true
        type: string
      - in: query
        minimum: 1
        name: page
        required: true
        type: integer
      - in: query
        minimum: 1
        name: per_page
        required: true
        type: integer
      - in: query
        name: remote_ip
        type: string
      - in: query
        name: subject
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  $ref: '#/definitions/v1.ConversationListItems'
              type: object
      summary: get conversation list
      tags:
      - conversation
  /api/v1/conversation/detail:
    get:
      consumes:
      - application/json
      description: get conversation detail
      parameters:
      - description: user id
        in: header
        name: X-SafePoint-User-ID
        required: true
        type: string
      - description: conversation id
        in: query
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  $ref: '#/definitions/domain.ConversationDetailResp'
              type: object
      summary: get conversation detail
      tags:
      - conversation
  /api/v1/conversation/message/detail:
    get:
      consumes:
      - application/json
      description: Get message detail
      parameters:
      - description: message id
        in: query
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  $ref: '#/definitions/domain.ConversationMessage'
              type: object
      summary: Get message detail
      tags:
      - Message
  /api/v1/conversation/message/list:
    get:
      consumes:
      - application/json
      description: GetMessageFeedBackList
      parameters:
      - in: query
        name: kb_id
        required: true
        type: string
      - in: query
        minimum: 1
        name: page
        required: true
        type: integer
      - in: query
        minimum: 1
        name: per_page
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: MessageList
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  $ref: '#/definitions/domain.PaginatedResult-array_domain_ConversationMessageListItem'
              type: object
      summary: GetMessageFeedBackList
      tags:
      - Message
  /api/v1/crawler/confluence/analysis_export_file:
    post:
      consumes:
      - application/json
      description: Analyze Confluence Export File
      parameters:
      - description: file
        in: formData
        name: file
        required: true
        type: file
      - description: kb_id
        in: formData
        name: kb_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/domain.AnalysisConfluenceResp'
                  type: array
              type: object
      summary: AnalysisConfluenceExportFile
      tags:
      - crawler
  /api/v1/crawler/epub/convert:
    post:
      consumes:
      - multipart/form-data
      description: QpubConvert
      parameters:
      - description: file
        in: formData
        name: file
        required: true
        type: file
      - description: kb_id
        in: formData
        name: kb_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  $ref: '#/definitions/domain.EpubResp'
              type: object
      summary: QpubConvert
      tags:
      - crawler
  /api/v1/crawler/feishu/get_doc:
    post:
      consumes:
      - application/json
      description: Get Docx in Feishu Spaces
      parameters:
      - description: Get Docx
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/domain.GetDocxReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/domain.GetDocxResp'
                  type: array
              type: object
      summary: FeishuGetDocx
      tags:
      - crawler
  /api/v1/crawler/feishu/list_doc:
    post:
      consumes:
      - application/json
      description: List Docx in Feishu Spaces
      parameters:
      - description: Search Docx
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/domain.SearchDocxReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/domain.SearchDocxResp'
                  type: array
              type: object
      summary: FeishuListDoc
      tags:
      - crawler
  /api/v1/crawler/feishu/list_spaces:
    post:
      consumes:
      - application/json
      description: List All Feishu Spaces
      parameters:
      - description: List Spaces
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/domain.GetSpaceListReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/domain.GetSpaceListResp'
                  type: array
              type: object
      summary: FeishuListSpaces
      tags:
      - crawler
  /api/v1/crawler/feishu/search_wiki:
    post:
      consumes:
      - application/json
      description: Search Wiki in Feishu Spaces
      parameters:
      - description: Search Wiki
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/domain.SearchWikiReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/domain.SearchWikiResp'
                  type: array
              type: object
      summary: FeishuSearchWiki
      tags:
      - crawler
  /api/v1/crawler/notion/get_doc:
    post:
      consumes:
      - application/json
      description: GetDocs
      parameters:
      - description: Get Docs
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/domain.GetDocsReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/domain.Page'
                  type: array
              type: object
      summary: GetDocs
      tags:
      - crawler
  /api/v1/crawler/notion/get_list:
    post:
      consumes:
      - application/json
      description: NotionGetList
      parameters:
      - description: Notion Get List
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/domain.NotnionGetListReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/domain.PageInfo'
                  type: array
              type: object
      summary: NotionGetList
      tags:
      - crawler
  /api/v1/crawler/parse_rss:
    post:
      consumes:
      - application/json
      description: Parse RSS
      parameters:
      - description: Parse URL
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/domain.ParseURLReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  $ref: '#/definitions/domain.ParseURLResp'
              type: object
      summary: Parse RSS
      tags:
      - crawler
  /api/v1/crawler/parse_sitemap:
    post:
      consumes:
      - application/json
      description: Parse Sitemap
      parameters:
      - description: Parse URL
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/domain.ParseURLReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  $ref: '#/definitions/domain.ParseURLResp'
              type: object
      summary: Parse Sitemap
      tags:
      - crawler
  /api/v1/crawler/scrape:
    post:
      consumes:
      - application/json
      description: Scrape
      parameters:
      - description: Scrape
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/domain.ScrapeReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  $ref: '#/definitions/domain.ScrapeResp'
              type: object
      summary: Scrape
      tags:
      - crawler
  /api/v1/crawler/wikijs/analysis_export_file:
    post:
      consumes:
      - multipart/form-data
      description: AnalysisWikijsExportFile
      parameters:
      - description: file
        in: formData
        name: file
        required: true
        type: file
      - description: kb_id
        in: formData
        name: kb_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/domain.WikiJSResp'
                  type: array
              type: object
      summary: AnalysisWikijsExportFile
      tags:
      - crawler
  /api/v1/creation/text:
    post:
      consumes:
      - application/json
      description: Text creation
      parameters:
      - description: text creation request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/domain.TextReq'
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            type: string
      summary: Text creation
      tags:
      - creation
  /api/v1/file/upload:
    post:
      consumes:
      - multipart/form-data
      description: Upload File
      parameters:
      - description: File
        in: formData
        name: file
        required: true
        type: file
      - description: Knowledge Base ID
        in: formData
        name: kb_id
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.ObjectUploadResp'
      summary: Upload File
      tags:
      - file
  /api/v1/knowledge_base:
    post:
      consumes:
      - application/json
      description: CreateKnowledgeBase
      parameters:
      - description: CreateKnowledgeBase Request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/domain.CreateKnowledgeBaseReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: CreateKnowledgeBase
      tags:
      - knowledge_base
  /api/v1/knowledge_base/detail:
    delete:
      consumes:
      - application/json
      description: DeleteKnowledgeBase
      parameters:
      - description: Knowledge Base ID
        in: query
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: DeleteKnowledgeBase
      tags:
      - knowledge_base
    get:
      consumes:
      - application/json
      description: GetKnowledgeBaseDetail
      parameters:
      - description: Knowledge Base ID
        in: query
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  $ref: '#/definitions/domain.KnowledgeBaseDetail'
              type: object
      summary: GetKnowledgeBaseDetail
      tags:
      - knowledge_base
    put:
      consumes:
      - application/json
      description: UpdateKnowledgeBase
      parameters:
      - description: UpdateKnowledgeBase Request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/domain.UpdateKnowledgeBaseReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: UpdateKnowledgeBase
      tags:
      - knowledge_base
  /api/v1/knowledge_base/list:
    get:
      consumes:
      - application/json
      description: GetKnowledgeBaseList
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/domain.KnowledgeBaseListItem'
                  type: array
              type: object
      summary: GetKnowledgeBaseList
      tags:
      - knowledge_base
  /api/v1/knowledge_base/release:
    post:
      consumes:
      - application/json
      description: CreateKBRelease
      parameters:
      - description: CreateKBRelease Request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/domain.CreateKBReleaseReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: CreateKBRelease
      tags:
      - knowledge_base
  /api/v1/knowledge_base/release/list:
    get:
      consumes:
      - application/json
      description: GetKBReleaseList
      parameters:
      - description: Knowledge Base ID
        in: query
        name: kb_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  $ref: '#/definitions/domain.GetKBReleaseListResp'
              type: object
      summary: GetKBReleaseList
      tags:
      - knowledge_base
  /api/v1/model:
    post:
      consumes:
      - application/json
      description: create model
      parameters:
      - description: create model request
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/domain.CreateModelReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: create model
      tags:
      - model
    put:
      consumes:
      - application/json
      description: update model
      parameters:
      - description: update model request
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/domain.UpdateModelReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      tags:
      - model
  /api/v1/model/check:
    post:
      consumes:
      - application/json
      description: check model
      parameters:
      - description: check model request
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/domain.CheckModelReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  $ref: '#/definitions/domain.CheckModelResp'
              type: object
      summary: check model
      tags:
      - model
  /api/v1/model/detail:
    get:
      consumes:
      - application/json
      description: get model detail
      parameters:
      - description: model id
        in: query
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  $ref: '#/definitions/domain.ModelDetailResp'
              type: object
      summary: get model detail
      tags:
      - model
  /api/v1/model/list:
    get:
      consumes:
      - application/json
      description: get model list
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  $ref: '#/definitions/domain.ModelListItem'
              type: object
      summary: get model list
      tags:
      - model
  /api/v1/model/provider/supported:
    get:
      consumes:
      - application/json
      description: get provider supported model list
      parameters:
      - in: query
        name: api_header
        type: string
      - in: query
        name: api_key
        type: string
      - in: query
        name: base_url
        required: true
        type: string
      - enum:
        - SiliconFlow
        - OpenAI
        - Ollama
        - DeepSeek
        - Moonshot
        - AzureOpenAI
        - BaiZhiCloud
        - Hunyuan
        - BaiLian
        - Volcengine
        - Gemini
        in: query
        name: provider
        required: true
        type: string
      - enum:
        - chat
        - embedding
        - rerank
        in: query
        name: type
        required: true
        type: string
        x-enum-varnames:
        - ModelTypeChat
        - ModelTypeEmbedding
        - ModelTypeRerank
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  $ref: '#/definitions/domain.GetProviderModelListResp'
              type: object
      summary: get provider supported model list
      tags:
      - model
  /api/v1/node:
    post:
      consumes:
      - application/json
      description: Create Node
      parameters:
      - description: Node
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/domain.CreateNodeReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  additionalProperties:
                    type: string
                  type: object
              type: object
      summary: Create Node
      tags:
      - node
  /api/v1/node/action:
    post:
      consumes:
      - application/json
      description: Node Action
      parameters:
      - description: Action
        in: body
        name: action
        required: true
        schema:
          $ref: '#/definitions/domain.NodeActionReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  additionalProperties:
                    type: string
                  type: object
              type: object
      summary: Node Action
      tags:
      - node
  /api/v1/node/batch_move:
    post:
      consumes:
      - application/json
      description: Batch Move Node
      parameters:
      - description: Batch Move Node
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/domain.BatchMoveReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: Batch Move Node
      tags:
      - node
  /api/v1/node/detail:
    get:
      consumes:
      - application/json
      description: Get Node Detail
      parameters:
      - description: ID
        in: query
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  $ref: '#/definitions/domain.NodeDetailResp'
              type: object
      summary: Get Node Detail
      tags:
      - node
    put:
      consumes:
      - application/json
      description: Update Node Detail
      parameters:
      - description: Node
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/domain.UpdateNodeReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: Update Node Detail
      tags:
      - node
  /api/v1/node/list:
    get:
      consumes:
      - application/json
      description: Get Node List
      parameters:
      - in: query
        name: kb_id
        required: true
        type: string
      - in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/domain.NodeListItemResp'
                  type: array
              type: object
      summary: Get Node List
      tags:
      - node
  /api/v1/node/move:
    post:
      consumes:
      - application/json
      description: Move Node
      parameters:
      - description: Move Node
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/domain.MoveNodeReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: Move Node
      tags:
      - node
  /api/v1/node/recommend_nodes:
    get:
      consumes:
      - application/json
      description: Recommend Nodes
      parameters:
      - in: query
        name: kb_id
        required: true
        type: string
      - collectionFormat: csv
        in: query
        items:
          type: string
        name: node_ids
        required: true
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/domain.RecommendNodeListResp'
                  type: array
              type: object
      summary: Recommend Nodes
      tags:
      - node
  /api/v1/node/release/detail:
    get:
      consumes:
      - application/json
      description: Get Node Release Detail
      parameters:
      - in: query
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  $ref: '#/definitions/domain.GetNodeReleaseDetailResp'
              type: object
      summary: Get Node Release Detail
      tags:
      - node
  /api/v1/node/release/list:
    get:
      consumes:
      - application/json
      description: Get Node Release List
      parameters:
      - in: query
        name: kb_id
        required: true
        type: string
      - in: query
        name: node_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/domain.NodeReleaseListItem'
                  type: array
              type: object
      summary: Get Node Release List
      tags:
      - node
  /api/v1/node/summary:
    post:
      consumes:
      - application/json
      description: Summary Node
      parameters:
      - description: Summary Node
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/domain.NodeSummaryReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: Summary Node
      tags:
      - node
  /api/v1/stat/browsers:
    get:
      consumes:
      - application/json
      description: GetBrowsers
      parameters:
      - description: kb_id
        in: query
        name: kb_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: GetBrowsers
      tags:
      - stat
  /api/v1/stat/conversation_distribution:
    get:
      consumes:
      - application/json
      description: GetConversationDistribution
      parameters:
      - description: kb_id
        in: query
        name: kb_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: GetConversationDistribution
      tags:
      - stat
  /api/v1/stat/count:
    get:
      consumes:
      - application/json
      description: GetCount
      parameters:
      - description: kb_id
        in: query
        name: kb_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: GetCount
      tags:
      - stat
  /api/v1/stat/geo_count:
    get:
      consumes:
      - application/json
      description: GetGeoCount
      parameters:
      - description: kb_id
        in: query
        name: kb_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: GetGeoCount
      tags:
      - stat
  /api/v1/stat/hot_pages:
    get:
      consumes:
      - application/json
      description: GetHotPages
      parameters:
      - description: kb_id
        in: query
        name: kb_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: GetHotPages
      tags:
      - stat
  /api/v1/stat/instant_count:
    get:
      consumes:
      - application/json
      description: GetInstantCount
      parameters:
      - description: kb_id
        in: query
        name: kb_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: GetInstantCount
      tags:
      - stat
  /api/v1/stat/instant_pages:
    get:
      consumes:
      - application/json
      description: GetInstantPages
      parameters:
      - description: kb_id
        in: query
        name: kb_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: GetInstantPages
      tags:
      - stat
  /api/v1/stat/referer_hosts:
    get:
      consumes:
      - application/json
      description: GetRefererHosts
      parameters:
      - description: kb_id
        in: query
        name: kb_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: GetRefererHosts
      tags:
      - stat
  /api/v1/user:
    get:
      consumes:
      - application/json
      description: GetUser
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.UserInfoResp'
      summary: GetUser
      tags:
      - user
  /api/v1/user/create:
    post:
      consumes:
      - application/json
      description: CreateUser
      parameters:
      - description: CreateUser Request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/domain.CreateUserReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: CreateUser
      tags:
      - user
  /api/v1/user/delete:
    delete:
      consumes:
      - application/json
      description: DeleteUser
      parameters:
      - description: DeleteUser Request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/domain.DeleteUserReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: DeleteUser
      tags:
      - user
  /api/v1/user/list:
    get:
      consumes:
      - application/json
      description: ListUsers
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/domain.UserListItemResp'
                  type: array
              type: object
      summary: ListUsers
      tags:
      - user
  /api/v1/user/login:
    post:
      consumes:
      - application/json
      description: Login
      parameters:
      - description: Login Request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/domain.LoginReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.LoginResp'
      summary: Login
      tags:
      - user
  /api/v1/user/reset_password:
    put:
      consumes:
      - application/json
      description: ResetPassword
      parameters:
      - description: ResetPassword Request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/domain.ResetPasswordReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: ResetPassword
      tags:
      - user
  /share/v1/app/web/info:
    get:
      consumes:
      - application/json
      description: GetAppInfo
      parameters:
      - description: kb id
        in: header
        name: X-KB-ID
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: GetAppInfo
      tags:
      - share_app
  /share/v1/app/widget/info:
    get:
      consumes:
      - application/json
      description: GetWidgetAppInfo
      parameters:
      - description: kb id
        in: header
        name: X-KB-ID
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: GetWidgetAppInfo
      tags:
      - share_app
  /share/v1/chat/feedback:
    post:
      consumes:
      - application/json
      description: Process user feedback for chat conversations
      parameters:
      - description: feedback request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.FeedbackRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: Handle chat feedback
      tags:
      - share_chat
  /share/v1/chat/message:
    post:
      consumes:
      - application/json
      description: ChatMessage
      parameters:
      - description: app type
        in: query
        name: app_type
        required: true
        type: string
      - description: request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.ChatRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: ChatMessage
      tags:
      - share_chat
  /share/v1/chat/widget:
    post:
      consumes:
      - application/json
      description: ChatWidget
      parameters:
      - description: app type
        in: query
        name: app_type
        required: true
        type: string
      - description: request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.ChatRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: ChatWidget
      tags:
      - share_chat
  /share/v1/comment:
    post:
      consumes:
      - application/json
      description: CreateComment
      parameters:
      - description: Comment
        in: body
        name: comment
        required: true
        schema:
          $ref: '#/definitions/domain.CommentReq'
      produces:
      - application/json
      responses:
        "200":
          description: CommentID
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  type: string
              type: object
      summary: CreateComment
      tags:
      - share_comment
  /share/v1/comment/list:
    get:
      consumes:
      - application/json
      description: GetCommentList
      parameters:
      - description: nodeID
        in: query
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: CommentList
          schema:
            allOf:
            - $ref: '#/definitions/domain.Response'
            - properties:
                data:
                  $ref: '#/definitions/share.ShareCommentLists'
              type: object
      summary: GetCommentList
      tags:
      - share_comment
  /share/v1/node/detail:
    get:
      consumes:
      - application/json
      description: GetNodeDetail
      parameters:
      - description: kb id
        in: header
        name: X-KB-ID
        required: true
        type: string
      - description: node id
        in: query
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: GetNodeDetail
      tags:
      - share_node
  /share/v1/node/list:
    get:
      consumes:
      - application/json
      description: GetNodeList
      parameters:
      - description: kb id
        in: header
        name: X-KB-ID
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: GetNodeList
      tags:
      - share_node
  /share/v1/stat/page:
    post:
      consumes:
      - application/json
      description: RecordPage
      parameters:
      - description: request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.StatPageReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.Response'
      summary: RecordPage
      tags:
      - share_stat
swagger: "2.0"
