FROM alpine:3.20

RUN apk update \
    && apk upgrade \
    && apk add --no-cache ca-certificates tzdata \
    && update-ca-certificates 2>/dev/null || true \
    && rm -rf /var/cache/apk/*

WORKDIR /app

# 复制本地构建的二进制文件
COPY panda-wiki-api /app/panda-wiki-api
COPY panda-wiki-migrate /app/panda-wiki-migrate

# 设置权限
RUN chmod +x /app/panda-wiki-api /app/panda-wiki-migrate

# 创建数据目录
RUN mkdir -p /app/data /app/run

# 默认命令：先运行迁移，再启动 API
CMD ["sh", "-c", "/app/panda-wiki-migrate && /app/panda-wiki-api"]
