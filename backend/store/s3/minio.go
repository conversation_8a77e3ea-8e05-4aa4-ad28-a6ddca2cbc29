package s3

import (
	"context"
	"fmt"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"

	"github.com/chaitin/panda-wiki/config"
	"github.com/chaitin/panda-wiki/domain"
)

type MinioClient struct {
	*minio.Client
	config *config.Config
}

func NewMinioClient(config *config.Config) (*MinioClient, error) {
	endpoint := config.S3.Endpoint
	accessKey := config.S3.AccessKey
	secretKey := config.S3.SecretKey

	// 如果 S3 配置为空，返回一个空的客户端
	if endpoint == "" || accessKey == "" || secretKey == "" {
		return &MinioClient{Client: nil, config: config}, nil
	}

	minioClient, err := minio.New(endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(accessKey, secretKey, ""),
		Secure: false,
	})
	if err != nil {
		// 如果连接失败，记录错误但不崩溃
		fmt.Printf("Warning: Failed to connect to MinIO: %v\n", err)
		return &MinioClient{Client: nil, config: config}, nil
	}

	// check bucket
	bucket := domain.Bucket
	exists, err := minioClient.BucketExists(context.Background(), bucket)
	if err != nil {
		// 如果检查 bucket 失败，记录错误但不崩溃
		fmt.Printf("Warning: Failed to check MinIO bucket: %v\n", err)
		return &MinioClient{Client: nil, config: config}, nil
	}
	if !exists {
		err = minioClient.MakeBucket(context.Background(), bucket, minio.MakeBucketOptions{
			Region: "us-east-1",
		})
		if err != nil {
			fmt.Printf("Warning: Failed to create MinIO bucket: %v\n", err)
			return &MinioClient{Client: nil, config: config}, nil
		}
		err = minioClient.SetBucketPolicy(context.Background(), bucket, `{
			"Version": "2012-10-17",
			"Statement": [
				{
					"Action": ["s3:GetObject"],
					"Effect": "Allow",
					"Principal": "*",
					"Resource": ["arn:aws:s3:::static-file/*"],
					"Sid": "PublicRead"
				}
			]
		}`)
		if err != nil {
			fmt.Printf("Warning: Failed to set MinIO bucket policy: %v\n", err)
			return &MinioClient{Client: nil, config: config}, nil
		}
	}
	return &MinioClient{Client: minioClient, config: config}, nil
}

// sign url
func (c *MinioClient) SignURL(ctx context.Context, bucket, object string, expires time.Duration) (string, error) {
	url, err := c.PresignedGetObject(ctx, bucket, object, expires, nil)
	if err != nil {
		return "", err
	}
	return url.String(), nil
}
