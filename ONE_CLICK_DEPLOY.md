# PandaWiki 一键部署指南

🚀 **在任何有Docker环境的Mac或Linux电脑上，只需一条命令即可完成PandaWiki的完整部署！**

## 🎯 快速开始

### 方法一：使用一键部署脚本（推荐）

```bash
# 1. 克隆项目（或下载项目文件）
git clone <repository-url>
cd baidadoc-wiki

# 2. 给脚本执行权限
chmod +x one-click-deploy.sh

# 3. 一键部署
./one-click-deploy.sh
```

### 方法二：直接使用 Docker Compose

```bash
# 1. 确保在项目根目录
cd baidadoc-wiki

# 2. 一键部署
docker-compose -f docker-compose.prod.yml up -d --build
```

## 📋 系统要求

- **操作系统**: macOS 或 Linux
- **Docker**: 版本 20.10+ 
- **Docker Compose**: 版本 1.29+ 或 Docker Compose V2
- **内存**: 至少 4GB 可用内存
- **磁盘**: 至少 10GB 可用空间
- **端口**: 确保以下端口未被占用
  - `5432` - PostgreSQL
  - `6379` - Redis  
  - `4222` - NATS
  - `9000` - MinIO API
  - `9001` - MinIO Console
  - `8000` - PandaWiki API
  - `5173` - 管理后台
  - `3010` - 用户前台

## 🔧 部署过程

部署脚本会自动完成以下步骤：

1. ✅ **环境检查** - 验证Docker环境和端口可用性
2. ✅ **目录创建** - 创建必要的数据目录
3. ✅ **镜像构建** - 自动构建所有必需的Docker镜像
4. ✅ **服务启动** - 启动所有服务容器
5. ✅ **健康检查** - 等待服务完全启动

整个过程通常需要 **5-15分钟**，具体时间取决于网络速度和硬件性能。

## 🌐 访问地址

部署完成后，可以通过以下地址访问：

| 服务 | 地址 | 说明 |
|------|------|------|
| **用户前台** | http://localhost:3010 | 主要的知识库界面 |
| **管理后台** | http://localhost:5173 | 系统管理界面 |
| **API接口** | http://localhost:8000 | RESTful API |
| **MinIO控制台** | http://localhost:9001 | 对象存储管理 |

## 🔑 默认登录信息

### 管理后台登录
- **用户名**: `admin`
- **密码**: `admin123`

### MinIO控制台登录  
- **用户名**: `pandawiki`
- **密码**: `eMu4Gf9S1GJr5AZb6GiAuf12k064ei6OfnWTFytPXDM=`

> ⚠️ **安全提示**: 生产环境请立即修改默认密码！

## 🛠️ 常用管理命令

```bash
# 查看服务状态
docker-compose -f docker-compose.prod.yml ps

# 查看所有服务日志
docker-compose -f docker-compose.prod.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.prod.yml logs -f panda-wiki-api

# 重启所有服务
docker-compose -f docker-compose.prod.yml restart

# 停止所有服务
docker-compose -f docker-compose.prod.yml down

# 停止并删除所有数据（危险操作）
docker-compose -f docker-compose.prod.yml down -v
```

## 🔍 故障排除

### 常见问题

**1. 端口冲突**
```bash
# 检查端口占用
lsof -i :8000
# 或
netstat -tuln | grep :8000
```

**2. Docker权限问题**
```bash
# 将用户添加到docker组（Linux）
sudo usermod -aG docker $USER
# 重新登录或重启终端
```

**3. 内存不足**
```bash
# 清理Docker缓存
docker system prune -a
```

**4. 服务启动失败**
```bash
# 查看详细错误日志
docker-compose -f docker-compose.prod.yml logs service-name
```

### 重新部署

如果需要完全重新部署：

```bash
# 1. 停止并删除所有容器和数据
docker-compose -f docker-compose.prod.yml down -v

# 2. 清理镜像（可选）
docker system prune -a

# 3. 重新部署
./one-click-deploy.sh
```

## 📊 服务架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Admin Panel   │    │   User App      │    │   API Server    │
│   (Port 5173)   │    │   (Port 3010)   │    │   (Port 8000)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │     Redis       │    │     MinIO       │
│   (Port 5432)   │    │   (Port 6379)   │    │   (Port 9000)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                 │
                       ┌─────────────────┐
                       │      NATS       │
                       │   (Port 4222)   │
                       └─────────────────┘
```

## 🔒 生产环境建议

1. **修改默认密码**
2. **配置HTTPS** (使用Nginx/Caddy反向代理)
3. **设置防火墙规则**
4. **定期备份数据**
5. **监控服务状态**

## 📞 获取帮助

如果遇到问题：
1. 查看项目文档
2. 检查GitHub Issues
3. 提交新的Issue

---

**🎉 享受使用 PandaWiki！**
