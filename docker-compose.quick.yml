version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: pandawiki-postgres
    environment:
      POSTGRES_DB: panda-wiki
      POSTGRES_USER: panda-wiki
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: pandawiki-redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - ./data/redis:/data
    restart: unless-stopped

  nats:
    image: nats:2.10-alpine
    container_name: pandawiki-nats
    ports:
      - "${NATS_PORT:-4222}:4222"
    command: ["--jetstream", "--store_dir", "/data"]
    volumes:
      - ./data/nats:/data
    restart: unless-stopped

  minio:
    image: minio/minio:latest
    container_name: pandawiki-minio
    environment:
      MINIO_ROOT_USER: s3panda-wiki
      MINIO_ROOT_PASSWORD: ${S3_SECRET_KEY}
    ports:
      - "${MINIO_PORT:-9000}:9000"
      - "${MINIO_CONSOLE_PORT:-9001}:9001"
    volumes:
      - ./data/minio:/data
    command: server /data --console-address ":9001"
    restart: unless-stopped

  # 如果有预构建的镜像，使用以下配置
  # api:
  #   image: pandawiki/api:latest
  #   container_name: pandawiki-api
  #   environment:
  #     - PG_DSN=host=postgres user=panda-wiki password=${POSTGRES_PASSWORD} dbname=panda-wiki port=5432 sslmode=disable TimeZone=Asia/Shanghai
  #     - REDIS_ADDR=redis:6379
  #     - MQ_NATS_SERVER=nats://nats:4222
  #     - S3_ENDPOINT=minio:9000
  #     - S3_ACCESS_KEY=s3panda-wiki
  #     - S3_SECRET_KEY=${S3_SECRET_KEY}
  #     - JWT_SECRET=${JWT_SECRET}
  #     - ADMIN_PASSWORD=${ADMIN_PASSWORD}
  #     - CADDY_API=disabled
  #     - MAX_KB=50
  #     - CRAWLER_SERVICE_URL=disabled
  #   ports:
  #     - "${API_PORT:-8000}:8000"
  #   depends_on:
  #     - postgres
  #     - redis
  #     - minio
  #     - nats
  #   restart: unless-stopped

  # admin:
  #   image: pandawiki/admin:latest
  #   container_name: pandawiki-admin
  #   ports:
  #     - "${ADMIN_PORT:-5173}:80"
  #   depends_on:
  #     - api
  #   restart: unless-stopped

  # app:
  #   image: pandawiki/app:latest
  #   container_name: pandawiki-app
  #   ports:
  #     - "${APP_PORT:-3010}:3010"
  #   depends_on:
  #     - api
  #   restart: unless-stopped
