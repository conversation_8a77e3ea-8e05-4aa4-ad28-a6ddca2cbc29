#!/bin/bash

# PandaWiki 最终部署脚本
# 解决所有网络问题的终极方案

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🐼 PandaWiki 终极部署脚本${NC}"
echo "=================================="

# 确保 caddy-mock.sh 存在
if [[ ! -f "caddy-mock.sh" ]]; then
    echo -e "${YELLOW}创建 caddy-mock.sh...${NC}"
    cat > caddy-mock.sh << 'EOF'
#!/bin/sh
mkdir -p /app/run
(
    while true; do
        rm -f /app/run/caddy-admin.sock
        echo 'HTTP/1.1 200 OK\r\nContent-Length: 2\r\n\r\n{}' | nc -l -U /app/run/caddy-admin.sock
        sleep 1
    done
) &
(
    while true; do
        echo 'HTTP/1.1 200 OK\r\nContent-Length: 2\r\n\r\n{}' | nc -l -p 2019
        sleep 1
    done
) &
wait
EOF
    chmod +x caddy-mock.sh
fi

echo -e "${BLUE}🚀 开始部署...${NC}"

# 尝试使用镜像源部署
echo -e "${YELLOW}使用DockerProxy镜像源部署...${NC}"
if docker-compose -f docker-compose.mirror.yml up -d --build; then
    echo -e "${GREEN}✅ 部署成功！${NC}"
    
    echo -e "${BLUE}⏳ 等待服务启动...${NC}"
    sleep 30
    
    echo -e "${GREEN}🎉 PandaWiki 部署完成！${NC}"
    echo
    echo -e "${BLUE}📋 服务访问地址：${NC}"
    echo -e "管理后台: ${GREEN}http://localhost:5173${NC}"
    echo -e "用户前台: ${GREEN}http://localhost:3010${NC}"
    echo -e "API 接口: ${GREEN}http://localhost:8000${NC}"
    echo -e "MinIO 控制台: ${GREEN}http://localhost:9001${NC}"
    echo
    echo -e "${BLUE}🔑 默认登录信息：${NC}"
    echo -e "管理员用户名: ${YELLOW}admin${NC}"
    echo -e "管理员密码: ${YELLOW}admin123${NC}"
    
else
    echo -e "${RED}❌ 镜像源部署失败，尝试原始配置...${NC}"
    
    if docker-compose -f docker-compose.prod.yml up -d --build; then
        echo -e "${GREEN}✅ 原始配置部署成功！${NC}"
    else
        echo -e "${RED}❌ 部署失败${NC}"
        echo "请检查网络连接或联系技术支持"
        exit 1
    fi
fi

echo -e "${GREEN}🎊 享受使用 PandaWiki 吧！${NC}"
