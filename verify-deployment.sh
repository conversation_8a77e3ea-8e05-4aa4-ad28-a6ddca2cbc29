#!/bin/bash

# PandaWiki 部署验证脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔍 PandaWiki 部署验证${NC}"
echo "=========================="

# 检查容器状态
check_containers() {
    echo -e "${BLUE}📦 检查容器状态...${NC}"
    
    local containers=(
        "panda-wiki-postgres"
        "panda-wiki-redis" 
        "panda-wiki-nats"
        "panda-wiki-minio"
        "panda-wiki-api"
        "panda-wiki-admin"
        "panda-wiki-app"
    )
    
    local all_running=true
    
    for container in "${containers[@]}"; do
        if docker ps --format "table {{.Names}}" | grep -q "^$container$"; then
            echo -e "  ✅ $container: ${GREEN}运行中${NC}"
        else
            echo -e "  ❌ $container: ${RED}未运行${NC}"
            all_running=false
        fi
    done
    
    if $all_running; then
        echo -e "${GREEN}✅ 所有容器都在运行${NC}"
    else
        echo -e "${RED}❌ 部分容器未运行${NC}"
    fi
    echo
}

# 检查服务健康状态
check_health() {
    echo -e "${BLUE}🏥 检查服务健康状态...${NC}"
    
    # 检查 PostgreSQL
    if docker exec panda-wiki-postgres pg_isready -U panda-wiki -d panda-wiki &> /dev/null; then
        echo -e "  ✅ PostgreSQL: ${GREEN}健康${NC}"
    else
        echo -e "  ❌ PostgreSQL: ${RED}不健康${NC}"
    fi
    
    # 检查 Redis
    if docker exec panda-wiki-redis redis-cli ping | grep -q "PONG"; then
        echo -e "  ✅ Redis: ${GREEN}健康${NC}"
    else
        echo -e "  ❌ Redis: ${RED}不健康${NC}"
    fi
    
    # 检查 MinIO
    if curl -f http://localhost:9000/minio/health/live &> /dev/null; then
        echo -e "  ✅ MinIO: ${GREEN}健康${NC}"
    else
        echo -e "  ❌ MinIO: ${RED}不健康${NC}"
    fi
    
    # 检查 API
    if curl -f http://localhost:8000/health &> /dev/null; then
        echo -e "  ✅ API: ${GREEN}健康${NC}"
    else
        echo -e "  ❌ API: ${RED}不健康${NC}"
    fi
    
    echo
}

# 检查端口访问
check_ports() {
    echo -e "${BLUE}🌐 检查端口访问...${NC}"
    
    local ports=(
        "5173:管理后台"
        "3010:用户前台" 
        "8000:API接口"
        "9001:MinIO控制台"
    )
    
    for port_info in "${ports[@]}"; do
        local port=$(echo $port_info | cut -d':' -f1)
        local service=$(echo $port_info | cut -d':' -f2)
        
        if curl -f http://localhost:$port &> /dev/null; then
            echo -e "  ✅ $service (端口 $port): ${GREEN}可访问${NC}"
        else
            echo -e "  ❌ $service (端口 $port): ${RED}不可访问${NC}"
        fi
    done
    
    echo
}

# 显示访问信息
show_access_info() {
    echo -e "${GREEN}🎉 部署验证完成！${NC}"
    echo
    echo -e "${BLUE}📋 访问地址：${NC}"
    echo -e "管理后台: ${GREEN}http://localhost:5173${NC}"
    echo -e "用户前台: ${GREEN}http://localhost:3010${NC}"
    echo -e "API 接口: ${GREEN}http://localhost:8000${NC}"
    echo -e "MinIO 控制台: ${GREEN}http://localhost:9001${NC}"
    echo
    echo -e "${BLUE}🔑 登录信息：${NC}"
    echo -e "管理员用户名: ${YELLOW}admin${NC}"
    echo -e "管理员密码: ${YELLOW}admin123${NC}"
    echo
}

# 主函数
main() {
    check_containers
    check_health
    check_ports
    show_access_info
}

# 执行验证
main "$@"
