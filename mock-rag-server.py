#!/usr/bin/env python3
"""
Simple mock RAG server for PandaWiki development
"""
import json
from http.server import HTTPServer, BaseHTTPRequestHandler
import threading
import time

class MockRAGHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/api/v1/models':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            # Mock response with some models
            response = {
                "data": [
                    {
                        "id": "bge-m3",
                        "object": "model",
                        "created": 1234567890,
                        "owned_by": "mock"
                    },
                    {
                        "id": "text-embedding-ada-002", 
                        "object": "model",
                        "created": 1234567890,
                        "owned_by": "mock"
                    }
                ]
            }
            self.wfile.write(json.dumps(response).encode())
        else:
            self.send_response(404)
            self.end_headers()
            self.wfile.write(b'Not Found')
    
    def do_POST(self):
        # Handle any POST requests
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        response = {"status": "ok", "message": "Mock RAG response"}
        self.wfile.write(json.dumps(response).encode())

    def do_DELETE(self):
        # Handle DELETE requests (like deleting knowledge bases)
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        response = {"success": True, "message": "Resource deleted successfully"}
        self.wfile.write(json.dumps(response).encode())

    def do_PUT(self):
        # Handle PUT requests
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        response = {"status": "ok", "message": "Mock RAG PUT response"}
        self.wfile.write(json.dumps(response).encode())
    
    def log_message(self, format, *args):
        # Suppress default logging
        pass

def start_mock_rag_server():
    server = HTTPServer(('localhost', 8080), MockRAGHandler)
    print("🤖 Mock RAG server started on http://localhost:8080")
    print("📡 Available endpoints:")
    print("   GET /api/v1/models - Returns mock model list")
    server.serve_forever()

if __name__ == '__main__':
    start_mock_rag_server()
