#!/bin/bash

# PandaWiki 一键部署脚本
# 适用于任何有Docker环境的Mac或Linux系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🐼 PandaWiki 一键部署脚本${NC}"
echo "=================================="
echo "此脚本将在任何有Docker环境的Mac或Linux系统上部署PandaWiki"
echo

# 检查系统要求
check_requirements() {
    echo -e "${BLUE}📋 检查系统要求...${NC}"
    
    # 检查操作系统
    if [[ "$OSTYPE" != "linux-gnu"* && "$OSTYPE" != "darwin"* ]]; then
        echo -e "${RED}❌ 不支持的操作系统: $OSTYPE${NC}"
        echo "此脚本仅支持 Linux 和 macOS"
        exit 1
    fi
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker 未安装${NC}"
        echo "请先安装 Docker: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        echo -e "${RED}❌ Docker Compose 未安装${NC}"
        echo "请先安装 Docker Compose"
        exit 1
    fi
    
    # 检查 Docker 是否运行
    if ! docker info &> /dev/null; then
        echo -e "${RED}❌ Docker 服务未运行${NC}"
        echo "请启动 Docker 服务"
        exit 1
    fi
    
    # 检查端口占用
    local ports=(5432 6379 4222 9000 9001 8000 5173 3010)
    local occupied_ports=()
    
    for port in "${ports[@]}"; do
        if command -v lsof &> /dev/null; then
            if lsof -i :$port &> /dev/null; then
                occupied_ports+=($port)
            fi
        elif command -v netstat &> /dev/null; then
            if netstat -tuln | grep ":$port " &> /dev/null; then
                occupied_ports+=($port)
            fi
        fi
    done
    
    if [ ${#occupied_ports[@]} -gt 0 ]; then
        echo -e "${YELLOW}⚠️  以下端口已被占用: ${occupied_ports[*]}${NC}"
        echo "这可能会导致部署失败，请确保这些端口可用"
        read -p "是否继续部署? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    echo -e "${GREEN}✅ 系统要求检查通过${NC}"
}

# 创建必要的目录和文件
setup_environment() {
    echo -e "${BLUE}🔧 设置环境...${NC}"
    
    # 创建数据目录
    mkdir -p data/{postgres,redis,minio,nats}
    
    # 确保 caddy-mock.sh 存在
    if [[ ! -f "caddy-mock.sh" ]]; then
        cat > caddy-mock.sh << 'EOF'
#!/bin/sh

# 创建运行目录
mkdir -p /app/run

# 启动 Unix socket 监听器（后台运行）
(
    while true; do
        rm -f /app/run/caddy-admin.sock
        echo 'HTTP/1.1 200 OK\r\nContent-Length: 2\r\n\r\n{}' | nc -l -U /app/run/caddy-admin.sock
        sleep 1
    done
) &

# 启动 HTTP 监听器（后台运行）
(
    while true; do
        echo 'HTTP/1.1 200 OK\r\nContent-Length: 2\r\n\r\n{}' | nc -l -p 2019
        sleep 1
    done
) &

# 保持脚本运行
wait
EOF
        chmod +x caddy-mock.sh
    fi
    
    echo -e "${GREEN}✅ 环境设置完成${NC}"
}

# 部署服务
deploy_services() {
    echo -e "${BLUE}🚀 开始部署服务...${NC}"
    echo "这可能需要几分钟时间，请耐心等待..."
    
    # 使用 docker-compose 部署
    if command -v docker-compose &> /dev/null; then
        docker-compose -f docker-compose.prod.yml up -d --build
    else
        docker compose -f docker-compose.prod.yml up -d --build
    fi
    
    echo -e "${GREEN}✅ 服务部署完成${NC}"
}

# 等待服务启动
wait_for_services() {
    echo -e "${BLUE}⏳ 等待服务启动...${NC}"
    
    local max_attempts=60
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if curl -f http://localhost:8000/health &> /dev/null; then
            echo -e "${GREEN}✅ API 服务已启动${NC}"
            break
        fi
        
        echo -n "."
        sleep 5
        ((attempt++))
    done
    
    if [ $attempt -eq $max_attempts ]; then
        echo -e "${YELLOW}⚠️  API 服务启动超时，但其他服务可能已正常运行${NC}"
    fi
    
    echo
}

# 显示部署结果
show_results() {
    echo -e "${GREEN}🎉 PandaWiki 部署完成！${NC}"
    echo
    echo -e "${BLUE}📋 服务访问地址：${NC}"
    echo -e "管理后台: ${GREEN}http://localhost:5173${NC}"
    echo -e "用户前台: ${GREEN}http://localhost:3010${NC}"
    echo -e "API 接口: ${GREEN}http://localhost:8000${NC}"
    echo -e "MinIO 控制台: ${GREEN}http://localhost:9001${NC}"
    echo
    echo -e "${BLUE}🔑 默认登录信息：${NC}"
    echo -e "管理员用户名: ${YELLOW}admin${NC}"
    echo -e "管理员密码: ${YELLOW}admin123${NC}"
    echo -e "MinIO 用户名: ${YELLOW}pandawiki${NC}"
    echo -e "MinIO 密码: ${YELLOW}eMu4Gf9S1GJr5AZb6GiAuf12k064ei6OfnWTFytPXDM=${NC}"
    echo
    echo -e "${BLUE}📝 常用命令：${NC}"
    echo "查看服务状态: docker-compose -f docker-compose.prod.yml ps"
    echo "查看日志: docker-compose -f docker-compose.prod.yml logs -f"
    echo "停止服务: docker-compose -f docker-compose.prod.yml down"
    echo "重启服务: docker-compose -f docker-compose.prod.yml restart"
    echo
    echo -e "${YELLOW}💡 提示：${NC}"
    echo "1. 首次启动可能需要几分钟来初始化数据库"
    echo "2. 如果服务无法访问，请检查防火墙设置"
    echo "3. 生产环境请修改默认密码"
}

# 主函数
main() {
    echo -e "${BLUE}开始一键部署流程...${NC}"
    echo
    
    check_requirements
    setup_environment
    deploy_services
    wait_for_services
    show_results
    
    echo -e "${GREEN}🎊 部署完成！享受使用 PandaWiki 吧！${NC}"
}

# 错误处理
trap 'echo -e "${RED}❌ 部署过程中发生错误${NC}"; exit 1' ERR

# 执行主函数
main "$@"
