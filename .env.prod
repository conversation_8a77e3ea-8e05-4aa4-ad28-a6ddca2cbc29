# PandaWiki Production Environment Configuration

# 基础配置
COMPOSE_PROJECT_NAME=panda-wiki
TZ=Asia/Shanghai

# 数据库配置
POSTGRES_DB=panda-wiki
POSTGRES_USER=panda-wiki
POSTGRES_PASSWORD=eMu4Gf9S1GJr5AZb6GiAuf12k064ei6OfnWTFytPXDM=
PG_DSN="host=panda-wiki-postgres user=panda-wiki password=eMu4Gf9S1GJr5AZb6GiAuf12k064ei6OfnWTFytPXDM= dbname=panda-wiki port=5432 sslmode=disable TimeZone=Asia/Shanghai"

# Redis配置
REDIS_ADDR=panda-wiki-redis:6379

# NATS配置
MQ_NATS_SERVER=nats://panda-wiki-nats:4222

# MinIO配置
S3_ENDPOINT=panda-wiki-minio:9000
S3_ACCESS_KEY=s3panda-wiki
S3_SECRET_KEY=9MzUsS7JhMlRMEBJpDIGhqYr5Ybjju4I2gs2PrPocPE=

# 应用配置
JWT_SECRET=roewc9gQ6FPGw-wOvjVgEPKGhRw03o-9cG6BOW58cgFp1cEs4Dki5N9NcUrdg5R-p2sRvEggh6-TjmPOqD1gyQ
ADMIN_PASSWORD=admin123
DATA_DIR=/app/data

# 端口配置
API_PORT=8000
ADMIN_PORT=5173
APP_PORT=3010
POSTGRES_PORT=5432
REDIS_PORT=6379
NATS_PORT=4222
MINIO_PORT=9000
MINIO_CONSOLE_PORT=9001

# 其他配置
CADDY_API=disabled
MAX_KB=50
CRAWLER_SERVICE_URL=disabled
RAG_CT_RAG_BASE_URL=http://localhost:8080/api/v1
