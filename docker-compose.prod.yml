services:
  # PostgreSQL数据库
  panda-wiki-postgres:
    image: postgres:15-alpine
    container_name: panda-wiki-postgres
    environment:
      POSTGRES_DB: panda-wiki
      POSTGRES_USER: panda-wiki
      POSTGRES_PASSWORD: eMu4Gf9S1GJr5AZb6GiAuf12k064ei6OfnWTFytPXDM=
      TZ: Asia/Shanghai
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - panda-wiki-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U panda-wiki -d panda-wiki"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存
  panda-wiki-redis:
    image: redis:7-alpine
    container_name: panda-wiki-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - panda-wiki-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # NATS消息队列
  panda-wiki-nats:
    image: nats:2.10-alpine
    container_name: panda-wiki-nats
    ports:
      - "4222:4222"
      - "8222:8222"
    command: ["--jetstream", "--store_dir", "/data"]
    volumes:
      - nats_data:/data
    networks:
      - panda-wiki-network
    restart: unless-stopped

  # MinIO对象存储
  panda-wiki-minio:
    image: minio/minio:latest
    container_name: panda-wiki-minio
    environment:
      MINIO_ROOT_USER: pandawiki
      MINIO_ROOT_PASSWORD: eMu4Gf9S1GJr5AZb6GiAuf12k064ei6OfnWTFytPXDM=
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - panda-wiki-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PandaWiki API服务
  panda-wiki-api:
    image: pandawiki/api:latest
    container_name: panda-wiki-api
    environment:
      - PG_DSN=host=panda-wiki-postgres user=panda-wiki password=eMu4Gf9S1GJr5AZb6GiAuf12k064ei6OfnWTFytPXDM= dbname=panda-wiki port=5432 sslmode=disable TimeZone=Asia/Shanghai
      - REDIS_ADDR=panda-wiki-redis:6379
      - MQ_NATS_SERVER=nats://panda-wiki-nats:4222
      - S3_ENDPOINT=panda-wiki-minio:9000
      - S3_ACCESS_KEY=pandawiki
      - S3_SECRET_KEY=eMu4Gf9S1GJr5AZb6GiAuf12k064ei6OfnWTFytPXDM=
      - S3_USE_SSL=false
      - JWT_SECRET=eMu4Gf9S1GJr5AZb6GiAuf12k064ei6OfnWTFytPXDM=
      - ADMIN_PASSWORD=admin123
      - DATA_DIR=/data
      - CADDY_API=
      - MAX_KB=10240
      - CRAWLER_SERVICE_URL=
      - RAG_CT_RAG_BASE_URL=http://host.docker.internal:8080
      - RAG_DISABLED=true
      - TZ=Asia/Shanghai
    ports:
      - "8000:8000"
    volumes:
      - ./caddy-mock.sh:/app/caddy-mock.sh:ro
      - api_data:/app/data
    networks:
      - panda-wiki-network
    depends_on:
      panda-wiki-postgres:
        condition: service_healthy
      panda-wiki-redis:
        condition: service_healthy
      panda-wiki-minio:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PandaWiki 管理后台
  panda-wiki-admin:
    image: pandawiki/admin:latest
    container_name: panda-wiki-admin
    ports:
      - "5173:80"
    networks:
      - panda-wiki-network
    depends_on:
      - panda-wiki-api
    restart: unless-stopped

  # PandaWiki 用户前台
  panda-wiki-app:
    image: pandawiki/app:latest
    container_name: panda-wiki-app
    environment:
      - API_URL=http://**********:8000
      - TZ=Asia/Shanghai
    ports:
      - "3010:3010"
    networks:
      - panda-wiki-network
    depends_on:
      - panda-wiki-api
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  nats_data:
  minio_data:
  api_data:

networks:
  panda-wiki-network:
    driver: bridge
