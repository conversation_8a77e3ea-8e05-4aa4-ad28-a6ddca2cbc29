# PandaWiki Makefile for Docker deployment automation

# 项目配置
PROJECT_NAME := panda-wiki
COMPOSE_FILE := docker-compose.prod.yml
ENV_FILE := .env.prod
QUICK_COMPOSE_FILE := docker-compose.quick.yml
QUICK_ENV_FILE := .env

# Docker 镜像标签
API_IMAGE := pandawiki/api:latest
ADMIN_IMAGE := pandawiki/admin:latest
APP_IMAGE := pandawiki/app:latest

# 颜色定义
GREEN := \033[0;32m
YELLOW := \033[1;33m
BLUE := \033[0;34m
RED := \033[0;31m
NC := \033[0m # No Color

.PHONY: help check-deps build-api build-admin build-app build-all deploy quick-deploy start stop restart status logs backup cleanup

# 默认目标
help: ## 显示帮助信息
	@echo "$(BLUE)PandaWiki Docker 部署工具$(NC)"
	@echo "================================"
	@echo "可用命令："
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

check-deps: ## 检查依赖
	@echo "$(BLUE)检查依赖...$(NC)"
	@command -v docker >/dev/null 2>&1 || { echo "$(RED)Docker 未安装$(NC)"; exit 1; }
	@command -v docker-compose >/dev/null 2>&1 || docker compose version >/dev/null 2>&1 || { echo "$(RED)Docker Compose 未安装$(NC)"; exit 1; }
	@echo "$(GREEN)依赖检查通过$(NC)"

# 创建必要目录
create-dirs:
	@echo "$(BLUE)创建数据目录...$(NC)"
	@mkdir -p data/{postgres,redis,minio,nats}
	@mkdir -p backups

# 生成环境配置文件
generate-env:
	@echo "$(BLUE)生成环境配置...$(NC)"
	@if [ ! -f $(ENV_FILE) ]; then \
		echo "# PandaWiki Production Environment" > $(ENV_FILE); \
		echo "COMPOSE_PROJECT_NAME=$(PROJECT_NAME)" >> $(ENV_FILE); \
		echo "POSTGRES_PASSWORD=$$(openssl rand -base64 16 | tr -d '=+/' | cut -c1-16)" >> $(ENV_FILE); \
		echo "S3_SECRET_KEY=$$(openssl rand -base64 32 | tr -d '=+/' | cut -c1-32)" >> $(ENV_FILE); \
		echo "JWT_SECRET=$$(openssl rand -base64 64 | tr -d '=+/' | cut -c1-64)" >> $(ENV_FILE); \
		echo "ADMIN_PASSWORD=admin123" >> $(ENV_FILE); \
		echo "API_PORT=8000" >> $(ENV_FILE); \
		echo "ADMIN_PORT=5173" >> $(ENV_FILE); \
		echo "APP_PORT=3010" >> $(ENV_FILE); \
		echo "$(GREEN)环境配置文件已创建: $(ENV_FILE)$(NC)"; \
	else \
		echo "$(YELLOW)环境配置文件已存在: $(ENV_FILE)$(NC)"; \
	fi

# 构建 API 镜像
build-api: ## 构建后端 API 镜像
	@echo "$(BLUE)构建 API 镜像...$(NC)"
	@docker build -f backend/Dockerfile.api -t $(API_IMAGE) ./backend
	@echo "$(GREEN)API 镜像构建完成$(NC)"

# 构建管理后台镜像
build-admin: ## 构建管理后台镜像
	@echo "$(BLUE)构建管理后台镜像...$(NC)"
	@cd web/admin && npm install && npm run build
	@docker build -t $(ADMIN_IMAGE) ./web/admin
	@echo "$(GREEN)管理后台镜像构建完成$(NC)"

# 构建用户前台镜像
build-app: ## 构建用户前台镜像
	@echo "$(BLUE)构建用户前台镜像...$(NC)"
	@cd web/app && npm install && npm run build
	@docker build -t $(APP_IMAGE) ./web/app
	@echo "$(GREEN)用户前台镜像构建完成$(NC)"

# 构建所有镜像
build-all: build-api build-admin build-app ## 构建所有镜像
	@echo "$(GREEN)所有镜像构建完成$(NC)"

# 完整部署
deploy: check-deps create-dirs generate-env build-all ## 完整部署 PandaWiki
	@echo "$(BLUE)部署 PandaWiki...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) --env-file $(ENV_FILE) up -d
	@echo "$(GREEN)PandaWiki 部署完成！$(NC)"
	@$(MAKE) status

# 快速部署（仅基础服务）
quick-deploy: check-deps create-dirs ## 快速部署基础服务
	@echo "$(BLUE)快速部署基础服务...$(NC)"
	@if [ ! -f $(QUICK_ENV_FILE) ]; then \
		echo "POSTGRES_PASSWORD=$$(openssl rand -base64 16 | tr -d '=+/' | cut -c1-16)" > $(QUICK_ENV_FILE); \
		echo "S3_SECRET_KEY=$$(openssl rand -base64 32 | tr -d '=+/' | cut -c1-32)" >> $(QUICK_ENV_FILE); \
		echo "JWT_SECRET=$$(openssl rand -base64 64 | tr -d '=+/' | cut -c1-64)" >> $(QUICK_ENV_FILE); \
		echo "ADMIN_PASSWORD=admin123" >> $(QUICK_ENV_FILE); \
	fi
	@docker-compose -f $(QUICK_COMPOSE_FILE) --env-file $(QUICK_ENV_FILE) up -d
	@echo "$(GREEN)基础服务部署完成！$(NC)"

# 启动服务
start: ## 启动所有服务
	@echo "$(BLUE)启动服务...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) --env-file $(ENV_FILE) up -d
	@$(MAKE) status

# 停止服务
stop: ## 停止所有服务
	@echo "$(YELLOW)停止服务...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) --env-file $(ENV_FILE) down
	@echo "$(GREEN)服务已停止$(NC)"

# 重启服务
restart: ## 重启所有服务
	@echo "$(YELLOW)重启服务...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) --env-file $(ENV_FILE) restart
	@$(MAKE) status

# 显示服务状态
status: ## 显示服务状态
	@echo "$(BLUE)服务状态:$(NC)"
	@docker-compose -f $(COMPOSE_FILE) --env-file $(ENV_FILE) ps
	@echo ""
	@echo "$(BLUE)访问地址:$(NC)"
	@echo "管理后台: $(GREEN)http://localhost:5173$(NC)"
	@echo "用户前台: $(GREEN)http://localhost:3010$(NC)"
	@echo "API 接口: $(GREEN)http://localhost:8000$(NC)"
	@echo "MinIO 控制台: $(GREEN)http://localhost:9001$(NC)"

# 查看日志
logs: ## 查看所有服务日志
	@docker-compose -f $(COMPOSE_FILE) --env-file $(ENV_FILE) logs -f

# 查看特定服务日志
logs-api: ## 查看 API 服务日志
	@docker-compose -f $(COMPOSE_FILE) --env-file $(ENV_FILE) logs -f panda-wiki-api

logs-admin: ## 查看管理后台日志
	@docker-compose -f $(COMPOSE_FILE) --env-file $(ENV_FILE) logs -f panda-wiki-admin

logs-app: ## 查看用户前台日志
	@docker-compose -f $(COMPOSE_FILE) --env-file $(ENV_FILE) logs -f panda-wiki-app

# 备份数据
backup: ## 备份数据
	@echo "$(BLUE)创建备份...$(NC)"
	@BACKUP_NAME=panda-wiki-backup-$$(date +%Y%m%d-%H%M%S); \
	mkdir -p backups/$$BACKUP_NAME; \
	docker-compose -f $(COMPOSE_FILE) --env-file $(ENV_FILE) exec -T panda-wiki-postgres pg_dump -U panda-wiki panda-wiki > backups/$$BACKUP_NAME/database.sql; \
	docker cp panda-wiki-minio:/data backups/$$BACKUP_NAME/minio-data; \
	cp $(ENV_FILE) backups/$$BACKUP_NAME/; \
	echo "$(GREEN)备份完成: backups/$$BACKUP_NAME$(NC)"

# 更新服务
update: ## 更新到最新版本
	@echo "$(BLUE)更新 PandaWiki...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) --env-file $(ENV_FILE) pull
	@docker-compose -f $(COMPOSE_FILE) --env-file $(ENV_FILE) up -d
	@echo "$(GREEN)更新完成$(NC)"
	@$(MAKE) status

# 清理
cleanup: ## 清理所有容器和数据
	@echo "$(RED)警告: 这将删除所有容器、卷和镜像$(NC)"
	@read -p "确定要继续吗? (y/N): " confirm && [ "$$confirm" = "y" ] || exit 1
	@docker-compose -f $(COMPOSE_FILE) --env-file $(ENV_FILE) down -v --rmi all
	@echo "$(GREEN)清理完成$(NC)"

# 开发环境
dev-start: ## 启动开发环境
	@echo "$(BLUE)启动开发环境...$(NC)"
	@$(MAKE) quick-deploy
	@echo "$(YELLOW)基础服务已启动，请手动启动 API 和前端服务$(NC)"

# 健康检查
health: ## 检查服务健康状态
	@echo "$(BLUE)检查服务健康状态...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) --env-file $(ENV_FILE) exec panda-wiki-postgres pg_isready -U panda-wiki -d panda-wiki
	@docker-compose -f $(COMPOSE_FILE) --env-file $(ENV_FILE) exec panda-wiki-redis redis-cli ping
	@curl -f http://localhost:9000/minio/health/live || echo "MinIO health check failed"
	@curl -f http://localhost:8000/health || echo "API health check failed"

# 显示配置
show-config: ## 显示当前配置
	@echo "$(BLUE)当前配置:$(NC)"
	@cat $(ENV_FILE) 2>/dev/null || echo "配置文件不存在"
