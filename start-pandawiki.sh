#!/bin/bash

# PandaWiki 一键启动脚本
# 适用于新电脑快速部署

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}🐼 PandaWiki 一键启动脚本${NC}"
echo "================================"

# 检查 Docker 是否运行
echo -e "${BLUE}检查 Docker 状态...${NC}"
if ! docker info >/dev/null 2>&1; then
    echo -e "${RED}❌ Docker 未运行，请先启动 Docker${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Docker 运行正常${NC}"

# 检查 Docker Compose 是否可用
if ! command -v docker-compose >/dev/null 2>&1 && ! docker compose version >/dev/null 2>&1; then
    echo -e "${RED}❌ Docker Compose 未安装${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Docker Compose 可用${NC}"

# 创建必要的目录
echo -e "${BLUE}创建数据目录...${NC}"
mkdir -p data/{postgres,redis,minio,nats}
mkdir -p backups

# 生成环境配置文件（如果不存在）
ENV_FILE=".env.prod"
if [ ! -f "$ENV_FILE" ]; then
    echo -e "${BLUE}生成环境配置文件...${NC}"
    cat > "$ENV_FILE" << EOF
# PandaWiki Production Environment
COMPOSE_PROJECT_NAME=panda-wiki
POSTGRES_PASSWORD=$(openssl rand -base64 16 | tr -d '=+/' | cut -c1-16)
S3_SECRET_KEY=$(openssl rand -base64 32 | tr -d '=+/' | cut -c1-32)
JWT_SECRET=$(openssl rand -base64 64 | tr -d '=+/' | cut -c1-64)
ADMIN_PASSWORD=admin123
API_PORT=8000
ADMIN_PORT=5173
APP_PORT=3010
EOF
    echo -e "${GREEN}✅ 环境配置文件已创建: $ENV_FILE${NC}"
else
    echo -e "${YELLOW}⚠️  环境配置文件已存在: $ENV_FILE${NC}"
fi

# 检查并构建镜像
echo -e "${BLUE}检查 Docker 镜像...${NC}"

# 检查镜像是否存在，如果不存在则构建
IMAGES_TO_BUILD=()

if ! docker image inspect pandawiki/api:latest >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  API 镜像不存在，需要构建...${NC}"
    IMAGES_TO_BUILD+=("api")
fi

if ! docker image inspect pandawiki/admin:latest >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  管理后台镜像不存在，需要构建...${NC}"
    IMAGES_TO_BUILD+=("admin")
fi

if ! docker image inspect pandawiki/app:latest >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  用户前台镜像不存在，需要构建...${NC}"
    IMAGES_TO_BUILD+=("app")
fi

# 构建缺失的镜像
if [ ${#IMAGES_TO_BUILD[@]} -gt 0 ]; then
    echo -e "${BLUE}构建缺失的镜像...${NC}"
    echo -e "${YELLOW}⏳ 这可能需要几分钟时间...${NC}"

    for image in "${IMAGES_TO_BUILD[@]}"; do
        echo -e "${BLUE}构建 $image 镜像...${NC}"
        case $image in
            "api")
                if ! docker build -f backend/Dockerfile.api.pro -t pandawiki/api:latest ./backend; then
                    echo -e "${RED}❌ API 镜像构建失败${NC}"
                    exit 1
                fi
                ;;
            "admin")
                echo -e "${BLUE}构建管理后台前端...${NC}"
                cd web/admin
                if ! npm install --legacy-peer-deps; then
                    echo -e "${RED}❌ 管理后台依赖安装失败${NC}"
                    exit 1
                fi
                if ! npm run build; then
                    echo -e "${RED}❌ 管理后台构建失败${NC}"
                    exit 1
                fi
                cd ../..
                if ! docker build -t pandawiki/admin:latest ./web/admin; then
                    echo -e "${RED}❌ 管理后台镜像构建失败${NC}"
                    exit 1
                fi
                ;;
            "app")
                echo -e "${BLUE}构建用户前台...${NC}"
                cd web/app
                if ! npm install --legacy-peer-deps; then
                    echo -e "${RED}❌ 用户前台依赖安装失败${NC}"
                    exit 1
                fi
                if ! npm run build; then
                    echo -e "${RED}❌ 用户前台构建失败${NC}"
                    exit 1
                fi
                cd ../..
                if ! docker build -t pandawiki/app:latest ./web/app; then
                    echo -e "${RED}❌ 用户前台镜像构建失败${NC}"
                    exit 1
                fi
                ;;
        esac
        echo -e "${GREEN}✅ $image 镜像构建完成${NC}"
    done
else
    echo -e "${GREEN}✅ 所有镜像已存在${NC}"
fi

# 启动服务
echo -e "${BLUE}启动 PandaWiki 服务...${NC}"

if docker-compose -f docker-compose.prod.yml --env-file "$ENV_FILE" up -d; then
    echo -e "${GREEN}🎉 PandaWiki 启动成功！${NC}"
    echo ""
    echo -e "${BLUE}访问地址：${NC}"
    echo -e "🌐 用户前台：${GREEN}http://localhost:3010${NC}"
    echo -e "🔧 管理后台：${GREEN}http://localhost:5173${NC}"
    echo -e "🔌 API 接口：${GREEN}http://localhost:8000${NC}"
    echo -e "📦 MinIO 控制台：${GREEN}http://localhost:9001${NC}"
    echo ""
    echo -e "${YELLOW}💡 提示：${NC}"
    echo -e "   - 管理后台默认密码：admin123"
    echo -e "   - 查看服务状态：docker-compose -f docker-compose.prod.yml ps"
    echo -e "   - 查看日志：docker-compose -f docker-compose.prod.yml logs -f"
    echo -e "   - 停止服务：docker-compose -f docker-compose.prod.yml down"
else
    echo -e "${RED}❌ 启动失败，请检查错误信息${NC}"
    exit 1
fi
