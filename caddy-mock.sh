#!/bin/sh

# 创建运行目录
mkdir -p /app/run

# 启动 Unix socket 监听器（后台运行）
(
    while true; do
        rm -f /app/run/caddy-admin.sock
        echo 'HTTP/1.1 200 OK\r\nContent-Length: 2\r\n\r\n{}' | nc -l -U /app/run/caddy-admin.sock
        sleep 1
    done
) &

# 启动 HTTP 监听器（后台运行）
(
    while true; do
        echo 'HTTP/1.1 200 OK\r\nContent-Length: 2\r\n\r\n{}' | nc -l -p 2019
        sleep 1
    done
) &

# 保持脚本运行
wait
