#!/bin/bash

# Docker镜像加速器配置脚本
# 如果遇到镜像拉取超时，请先运行此脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 配置Docker镜像加速器${NC}"
echo "=================================="

# 检查操作系统
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo -e "${YELLOW}检测到macOS系统${NC}"
    echo "请手动配置Docker Desktop:"
    echo "1. 打开 Docker Desktop"
    echo "2. 进入 Settings -> Docker Engine"
    echo "3. 添加以下配置到JSON中:"
    echo
    cat << 'EOF'
{
  "registry-mirrors": [
    "https://docker.m.daocloud.io",
    "https://dockerproxy.com",
    "https://mirror.baidubce.com"
  ]
}
EOF
    echo
    echo "4. 点击 Apply & Restart"
    
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo -e "${YELLOW}检测到Linux系统，自动配置镜像加速器...${NC}"
    
    # 创建docker配置目录
    sudo mkdir -p /etc/docker
    
    # 备份原配置
    if [ -f /etc/docker/daemon.json ]; then
        sudo cp /etc/docker/daemon.json /etc/docker/daemon.json.backup
        echo -e "${YELLOW}已备份原配置文件${NC}"
    fi
    
    # 创建新配置
    sudo tee /etc/docker/daemon.json > /dev/null << 'EOF'
{
  "registry-mirrors": [
    "https://docker.m.daocloud.io",
    "https://dockerproxy.com", 
    "https://mirror.baidubce.com"
  ],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
EOF
    
    echo -e "${GREEN}✅ 配置文件已创建${NC}"
    
    # 重启Docker服务
    echo -e "${YELLOW}重启Docker服务...${NC}"
    sudo systemctl daemon-reload
    sudo systemctl restart docker
    
    echo -e "${GREEN}✅ Docker服务已重启${NC}"
    
else
    echo -e "${RED}不支持的操作系统: $OSTYPE${NC}"
    exit 1
fi

echo
echo -e "${BLUE}📋 可用的镜像加速器:${NC}"
echo "1. DaoCloud: https://docker.m.daocloud.io"
echo "2. DockerProxy: https://dockerproxy.com"
echo "3. 百度云: https://mirror.baidubce.com"
echo
echo -e "${GREEN}🎉 配置完成！现在可以重新尝试部署${NC}"
echo "运行: docker-compose -f docker-compose.prod.yml up -d"
