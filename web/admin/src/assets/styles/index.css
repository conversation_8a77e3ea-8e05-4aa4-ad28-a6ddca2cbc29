/*
  1. Use a more-intuitive box-sizing model.
*/
*,
*::before,
*::after {
  box-sizing: border-box;
}

/*
    2. Remove default margin
  */
* {
  margin: 0;
}

/*
    3. Allow percentage-based heights in the application
  */
html,
body {
  height: 100%;
  font-family: 'G';
}

/*
    Typographic tweaks!
    4. Add accessible line-height
    5. Improve text rendering
  */
body {
  line-height: 1.5;
}

/*
    6. Improve media defaults
  */
/* img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
} */

/*
    7. Remove built-in form typography styles
  */
input,
button,
textarea,
select {
  font: inherit;
}

/*
    8. Avoid text overflows
  */
p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

/*
    9. Create a root stacking context
  */
#root,
#__next {
  isolation: isolate;
}

body {
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code,
.code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

a {
  text-decoration: none;
}


::-webkit-scrollbar {
  width: 4px;
  /* 纵向滚动条*/
  height: 0;
  /* 横向滚动条隐藏 */
  border-radius: 10px;
}

/*定义滚动条轨道 内阴影*/
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0);
  background-color: #fff;
  border-radius: 10px;
}

/*定义滑块 内阴影*/
::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0);
  background-color: #ccc;
  border-radius: 10px;
}

.dark ::-webkit-scrollbar {
  width: 5px;
  /* 纵向滚动条*/
  height: 0;
  /* 横向滚动条隐藏 */
  background-color: #363636;
  border-radius: 10px;
}

/*定义滚动条轨道 内阴影*/
.dark ::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0);
  background-color: #363636;
  border-radius: 10px;
}

/*定义滑块 内阴影*/
.dark ::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0);
  background-color: #9b9b9b;
  border-radius: 10px;
}

@keyframes loadingRotate {
  from {
    transform: rotate(0);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes panda-wiki-scale {
  0% {
    transform: scale(0);
  }

  50% {
    transform: scale(1);
  }

  51% {
    transform: scale(1);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes panda-wiki-rotate {
  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(360deg);
  }

  51% {
    transform: rotate(360deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 适用于Chrome, Safari, Edge等Webkit浏览器 */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* 适用于Firefox */
input {
  -moz-appearance: textfield;
  appearance: textfield;
}

[class^="ellipsis-"] {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ellipsis-1 {
  -webkit-line-clamp: 1;
}

.ellipsis-2 {
  -webkit-line-clamp: 2;
}

.ellipsis-3 {
  -webkit-line-clamp: 3;
}

.ellipsis-5 {
  -webkit-line-clamp: 4;
}

.ellipsis-5 {
  -webkit-line-clamp: 5;
}