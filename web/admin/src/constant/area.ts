const Countries: Record<string, { cn: string; en: string }> = {
  CN: { cn: "中国", en: "China" },
  AD: { cn: "安道尔", en: "Andorra" },
  AE: { cn: "阿联酋", en: "United Arab Emirates" },
  AF: { cn: "阿富汗", en: "Afghanistan" },
  AG: { cn: "安提瓜和巴布达", en: "Antigua and Barbuda" },
  AI: { cn: "安圭拉", en: "Anguilla" },
  AL: { cn: "阿尔巴尼亚", en: "Albania" },
  AM: { cn: "亚美尼亚", en: "Armenia" },
  AO: { cn: "安哥拉", en: "Angola" },
  AQ: { cn: "南极洲", en: "Antarctica" },
  AR: { cn: "阿根廷", en: "Argentina" },
  AS: { cn: "美属萨摩亚", en: "American Samoa" },
  AT: { cn: "奥地利", en: "Austria" },
  AU: { cn: "澳大利亚", en: "Australia" },
  AW: { cn: "阿鲁巴", en: "Aruba" },
  AX: { cn: "奥兰", en: "Aland Islands" },
  AZ: { cn: "阿塞拜疆", en: "Azerbaijan" },
  BA: { cn: "波斯尼亚和黑塞哥维那", en: "Bosnia and Herzegovina" },
  BB: { cn: "巴巴多斯", en: "Barbados" },
  BD: { cn: "孟加拉国", en: "Bangladesh" },
  BE: { cn: "比利时", en: "Belgium" },
  BF: { cn: "布基纳法索", en: "Burkina Faso" },
  BG: { cn: "保加利亚", en: "Bulgaria" },
  BH: { cn: "巴林", en: "Bahrain" },
  BI: { cn: "布隆迪", en: "Burundi" },
  BJ: { cn: "贝宁", en: "Benin" },
  BL: { cn: "圣巴泰勒米", en: "Saint Barthelemy" },
  BM: { cn: "百慕大", en: "Bermuda" },
  BN: { cn: "文莱", en: "Brunei" },
  BO: { cn: "玻利维亚", en: "Bolivia" },
  BQ: { cn: "加勒比荷兰", en: "Bonaire, Saint Eustatius and Saba " },
  BR: { cn: "巴西", en: "Brazil" },
  BS: { cn: "巴哈马", en: "Bahamas" },
  BT: { cn: "不丹", en: "Bhutan" },
  BV: { cn: "布韦岛", en: "Bouvet Island" },
  BW: { cn: "博茨瓦纳", en: "Botswana" },
  BY: { cn: "白俄罗斯", en: "Belarus" },
  BZ: { cn: "伯利兹", en: "Belize" },
  CA: { cn: "加拿大", en: "Canada" },
  CC: { cn: "科科斯（基林）群岛", en: "Cocos Islands" },
  CD: { cn: "刚果（金）", en: "Democratic Republic of the Congo" },
  CF: { cn: "中非", en: "Central African Republic" },
  CG: { cn: "刚果（布）", en: "Republic of the Congo" },
  CH: { cn: "瑞士", en: "Switzerland" },
  CI: { cn: "科特迪瓦", en: "Ivory Coast" },
  CK: { cn: "库克群岛", en: "Cook Islands" },
  CL: { cn: "智利", en: "Chile" },
  CM: { cn: "喀麦隆", en: "Cameroon" },
  CO: { cn: "哥伦比亚", en: "Colombia" },
  CR: { cn: "哥斯达黎加", en: "Costa Rica" },
  CU: { cn: "古巴", en: "Cuba" },
  CV: { cn: "佛得角", en: "Cape Verde" },
  CW: { cn: "库拉索", en: "Curacao" },
  CX: { cn: "圣诞岛", en: "Christmas Island" },
  CY: { cn: "塞浦路斯", en: "Cyprus" },
  CZ: { cn: "捷克", en: "Czech Republic" },
  DE: { cn: "德国", en: "Germany" },
  DJ: { cn: "吉布提", en: "Djibouti" },
  DK: { cn: "丹麦", en: "Denmark" },
  DM: { cn: "多米尼克", en: "Dominica" },
  DO: { cn: "多米尼加", en: "Dominican Republic" },
  DZ: { cn: "阿尔及利亚", en: "Algeria" },
  EC: { cn: "厄瓜多尔", en: "Ecuador" },
  EE: { cn: "爱沙尼亚", en: "Estonia" },
  EG: { cn: "埃及", en: "Egypt" },
  EH: { cn: "阿拉伯撒哈拉民主共和国", en: "Western Sahara" },
  ER: { cn: "厄立特里亚", en: "Eritrea" },
  ES: { cn: "西班牙", en: "Spain" },
  ET: { cn: "埃塞俄比亚", en: "Ethiopia" },
  FI: { cn: "芬兰", en: "Finland" },
  FJ: { cn: "斐济", en: "Fiji" },
  FK: { cn: "福克兰群岛", en: "Falkland Islands" },
  FM: { cn: "密克罗尼西亚联邦", en: "Micronesia" },
  FO: { cn: "法罗群岛", en: "Faroe Islands" },
  FR: { cn: "法国", en: "France" },
  GA: { cn: "加蓬", en: "Gabon" },
  GB: { cn: "英国", en: "United Kingdom" },
  GD: { cn: "格林纳达", en: "Grenada" },
  GE: { cn: "格鲁吉亚", en: "Georgia" },
  GF: { cn: "法属圭亚那", en: "French Guiana" },
  GG: { cn: "根西", en: "Guernsey" },
  GH: { cn: "加纳", en: "Ghana" },
  GI: { cn: "直布罗陀", en: "Gibraltar" },
  GL: { cn: "格陵兰", en: "Greenland" },
  GM: { cn: "冈比亚", en: "Gambia" },
  GN: { cn: "几内亚", en: "Guinea" },
  GP: { cn: "瓜德罗普", en: "Guadeloupe" },
  GQ: { cn: "赤道几内亚", en: "Equatorial Guinea" },
  GR: { cn: "希腊", en: "Greece" },
  GS: {
    cn: "南乔治亚和南桑威奇群岛",
    en: "South Georgia and the South Sandwich Islands",
  },
  GT: { cn: "危地马拉", en: "Guatemala" },
  GU: { cn: "关岛", en: "Guam" },
  GW: { cn: "几内亚比绍", en: "Guinea-Bissau" },
  GY: { cn: "圭亚那", en: "Guyana" },
  HM: { cn: "赫德岛和麦克唐纳群岛", en: "Heard Island and McDonald Islands" },
  HN: { cn: "洪都拉斯", en: "Honduras" },
  HR: { cn: "克罗地亚", en: "Croatia" },
  HT: { cn: "海地", en: "Haiti" },
  HU: { cn: "匈牙利", en: "Hungary" },
  ID: { cn: "印尼", en: "Indonesia" },
  IE: { cn: "爱尔兰", en: "Ireland" },
  IL: { cn: "以色列", en: "Israel" },
  IM: { cn: "马恩岛", en: "Isle of Man" },
  IN: { cn: "印度", en: "India" },
  IO: { cn: "英属印度洋领地", en: "British Indian Ocean Territory" },
  IQ: { cn: "伊拉克", en: "Iraq" },
  IR: { cn: "伊朗", en: "Iran" },
  IS: { cn: "冰岛", en: "Iceland" },
  IT: { cn: "意大利", en: "Italy" },
  JE: { cn: "泽西", en: "Jersey" },
  JM: { cn: "牙买加", en: "Jamaica" },
  JO: { cn: "约旦", en: "Jordan" },
  JP: { cn: "日本", en: "Japan" },
  KE: { cn: "肯尼亚", en: "Kenya" },
  KG: { cn: "吉尔吉斯斯坦", en: "Kyrgyzstan" },
  KH: { cn: "柬埔寨", en: "Cambodia" },
  KI: { cn: "基里巴斯", en: "Kiribati" },
  KM: { cn: "科摩罗", en: "Comoros" },
  KN: { cn: "圣基茨和尼维斯", en: "Saint Kitts and Nevis" },
  KP: { cn: "朝鲜", en: "North Korea" },
  KR: { cn: "韩国", en: "South Korea" },
  KW: { cn: "科威特", en: "Kuwait" },
  KY: { cn: "开曼群岛", en: "Cayman Islands" },
  KZ: { cn: "哈萨克斯坦", en: "Kazakhstan" },
  LA: { cn: "老挝", en: "Laos" },
  LB: { cn: "黎巴嫩", en: "Lebanon" },
  LC: { cn: "圣卢西亚", en: "Saint Lucia" },
  LI: { cn: "列支敦士登", en: "Liechtenstein" },
  LK: { cn: "斯里兰卡", en: "Sri Lanka" },
  LR: { cn: "利比里亚", en: "Liberia" },
  LS: { cn: "莱索托", en: "Lesotho" },
  LT: { cn: "立陶宛", en: "Lithuania" },
  LU: { cn: "卢森堡", en: "Luxembourg" },
  LV: { cn: "拉脱维亚", en: "Latvia" },
  LY: { cn: "利比亚", en: "Libya" },
  MA: { cn: "摩洛哥", en: "Morocco" },
  MC: { cn: "摩纳哥", en: "Monaco" },
  MD: { cn: "摩尔多瓦", en: "Moldova" },
  ME: { cn: "黑山", en: "Montenegro" },
  MF: { cn: "法属圣马丁", en: "Saint Martin" },
  MG: { cn: "马达加斯加", en: "Madagascar" },
  MH: { cn: "马绍尔群岛", en: "Marshall Islands" },
  MK: { cn: "马其顿", en: "Macedonia" },
  ML: { cn: "马里", en: "Mali" },
  MM: { cn: "缅甸", en: "Myanmar" },
  MN: { cn: "蒙古", en: "Mongolia" },
  MP: { cn: "北马里亚纳群岛", en: "Northern Mariana Islands" },
  MQ: { cn: "马提尼克", en: "Martinique" },
  MR: { cn: "毛里塔尼亚", en: "Mauritania" },
  MS: { cn: "蒙特塞拉特", en: "Montserrat" },
  MT: { cn: "马耳他", en: "Malta" },
  MU: { cn: "毛里求斯", en: "Mauritius" },
  MV: { cn: "马尔代夫", en: "Maldives" },
  MW: { cn: "马拉维", en: "Malawi" },
  MX: { cn: "墨西哥", en: "Mexico" },
  MY: { cn: "马来西亚", en: "Malaysia" },
  MZ: { cn: "莫桑比克", en: "Mozambique" },
  NA: { cn: "纳米比亚", en: "Namibia" },
  NC: { cn: "新喀里多尼亚", en: "New Caledonia" },
  NE: { cn: "尼日尔", en: "Niger" },
  NF: { cn: "诺福克岛", en: "Norfolk Island" },
  NG: { cn: "尼日利亚", en: "Nigeria" },
  NI: { cn: "尼加拉瓜", en: "Nicaragua" },
  NL: { cn: "荷兰", en: "Netherlands" },
  NO: { cn: "挪威", en: "Norway" },
  NP: { cn: "尼泊尔", en: "Nepal" },
  NR: { cn: "瑙鲁", en: "Nauru" },
  NU: { cn: "纽埃", en: "Niue" },
  NZ: { cn: "新西兰", en: "New Zealand" },
  OM: { cn: "阿曼", en: "Oman" },
  PA: { cn: "巴拿马", en: "Panama" },
  PE: { cn: "秘鲁", en: "Peru" },
  PF: { cn: "法属波利尼西亚", en: "French Polynesia" },
  PG: { cn: "巴布亚新几内亚", en: "Papua New Guinea" },
  PH: { cn: "菲律宾", en: "Philippines" },
  PK: { cn: "巴基斯坦", en: "Pakistan" },
  PL: { cn: "波兰", en: "Poland" },
  PM: { cn: "圣皮埃尔和密克隆", en: "Saint Pierre and Miquelon" },
  PN: { cn: "皮特凯恩群岛", en: "Pitcairn" },
  PR: { cn: "波多黎各", en: "Puerto Rico" },
  PS: { cn: "巴勒斯坦", en: "Palestinian Territory" },
  PT: { cn: "葡萄牙", en: "Portugal" },
  PW: { cn: "帕劳", en: "Palau" },
  PY: { cn: "巴拉圭", en: "Paraguay" },
  QA: { cn: "卡塔尔", en: "Qatar" },
  RE: { cn: "留尼汪", en: "Reunion" },
  RO: { cn: "罗马尼亚", en: "Romania" },
  RS: { cn: "塞尔维亚", en: "Serbia" },
  RU: { cn: "俄罗斯", en: "Russia" },
  RW: { cn: "卢旺达", en: "Rwanda" },
  SA: { cn: "沙特阿拉伯", en: "Saudi Arabia" },
  SB: { cn: "所罗门群岛", en: "Solomon Islands" },
  SC: { cn: "塞舌尔", en: "Seychelles" },
  SD: { cn: "苏丹", en: "Sudan" },
  SE: { cn: "瑞典", en: "Sweden" },
  SG: { cn: "新加坡", en: "Singapore" },
  SH: { cn: "圣赫勒拿", en: "Saint Helena" },
  SI: { cn: "斯洛文尼亚", en: "Slovenia" },
  SJ: { cn: "挪威 斯瓦尔巴群岛和扬马延岛", en: "Svalbard and Jan Mayen" },
  SK: { cn: "斯洛伐克", en: "Slovakia" },
  SL: { cn: "塞拉利昂", en: "Sierra Leone" },
  SM: { cn: "圣马力诺", en: "San Marino" },
  SN: { cn: "塞内加尔", en: "Senegal" },
  SO: { cn: "索马里", en: "Somalia" },
  SR: { cn: "苏里南", en: "Suriname" },
  SS: { cn: "南苏丹", en: "South Sudan" },
  ST: { cn: "圣多美和普林西比", en: "Sao Tome and Principe" },
  SV: { cn: "萨尔瓦多", en: "El Salvador" },
  SX: { cn: "荷属圣马丁", en: "Sint Maarten" },
  SY: { cn: "叙利亚", en: "Syria" },
  SZ: { cn: "斯威士兰", en: "Swaziland" },
  TC: { cn: "特克斯和凯科斯群岛", en: "Turks and Caicos Islands" },
  TD: { cn: "乍得", en: "Chad" },
  TF: { cn: "法属南方和南极洲领地", en: "French Southern Territories" },
  TG: { cn: "多哥", en: "Togo" },
  TH: { cn: "泰国", en: "Thailand" },
  TJ: { cn: "塔吉克斯坦", en: "Tajikistan" },
  TK: { cn: "托克劳", en: "Tokelau" },
  TL: { cn: "东帝汶", en: "East Timor" },
  TM: { cn: "土库曼斯坦", en: "Turkmenistan" },
  TN: { cn: "突尼斯", en: "Tunisia" },
  TO: { cn: "汤加", en: "Tonga" },
  TR: { cn: "土耳其", en: "Turkey" },
  TT: { cn: "特立尼达和多巴哥", en: "Trinidad and Tobago" },
  TV: { cn: "图瓦卢", en: "Tuvalu" },
  TZ: { cn: "坦桑尼亚", en: "Tanzania" },
  UA: { cn: "乌克兰", en: "Ukraine" },
  UG: { cn: "乌干达", en: "Uganda" },
  UM: { cn: "美国本土外小岛屿", en: "United States Minor Outlying Islands" },
  US: { cn: "美国", en: "United States" },
  UY: { cn: "乌拉圭", en: "Uruguay" },
  UZ: { cn: "乌兹别克斯坦", en: "Uzbekistan" },
  VA: { cn: "梵蒂冈", en: "Vatican" },
  VC: { cn: "圣文森特和格林纳丁斯", en: "Saint Vincent and the Grenadines" },
  VE: { cn: "委内瑞拉", en: "Venezuela" },
  VG: { cn: "英属维尔京群岛", en: "British Virgin Islands" },
  VI: { cn: "美属维尔京群岛", en: "U.S. Virgin Islands" },
  VN: { cn: "越南", en: "Vietnam" },
  VU: { cn: "瓦努阿图", en: "Vanuatu" },
  WF: { cn: "瓦利斯和富图纳", en: "Wallis and Futuna" },
  WS: { cn: "萨摩亚", en: "Samoa" },
  YE: { cn: "也门", en: "Yemen" },
  YT: { cn: "马约特", en: "Mayotte" },
  ZA: { cn: "南非", en: "South Africa" },
  ZM: { cn: "赞比亚", en: "Zambia" },
  ZW: { cn: "津巴布韦", en: "Zimbabwe" },
};

const CountryOption = Object.entries(Countries).map(r => ({ name: r[1].en, value: r[0] }))

const ChinaProvinceSortName: Record<string, string> = {
  北京市: "北京",
  天津市: "天津",
  河北省: "河北",
  山西省: "山西",
  内蒙古自治区: "内蒙古",
  辽宁省: "辽宁",
  吉林省: "吉林",
  黑龙江省: "黑龙江",
  上海市: "上海",
  江苏省: "江苏",
  浙江省: "浙江",
  安徽省: "安徽",
  福建省: "福建",
  江西省: "江西",
  山东省: "山东",
  河南省: "河南",
  湖北省: "湖北",
  湖南省: "湖南",
  广东省: "广东",
  广西壮族自治区: "广西",
  海南省: "海南",
  重庆市: "重庆",
  四川省: "四川",
  贵州省: "贵州",
  云南省: "云南",
  西藏自治区: "西藏",
  陕西省: "陕西",
  甘肃省: "甘肃",
  青海省: "青海",
  宁夏回族自治区: "宁夏",
  新疆维吾尔自治区: "新疆",
  台湾省: "台湾",
  香港特别行政区: "香港",
  澳门特别行政区: "澳门",
};

const ChinaProvinceSortEnName: Record<string, string> = {
  上海: "Shanghai",
  云南: "Yunnan",
  内蒙古: "Inner Mongolia",
  北京: "Beijing",
  台湾: "Taiwan",
  吉林: "Jilin",
  四川: "Sichuan",
  天津: "Tianjin",
  宁夏: "Ningxia",
  安徽: "Anhui",
  山东: "Shandong",
  山西: "Shanxi",
  广东: "Guangdong",
  广西: "Guangxi",
  新疆: "Xinjiang",
  江苏: "Jiangsu",
  江西: "Jiangxi",
  河北: "Hebei",
  河南: "Henan",
  浙江: "Zhejiang",
  海南: "Hainan",
  湖北: "Hubei",
  湖南: "Hunan",
  澳门: "Macao",
  甘肃: "Gansu",
  福建: "Fujian",
  西藏: "Tibet",
  贵州: "Guizhou",
  辽宁: "Liaoning",
  重庆: "Chongqing",
  陕西: "Shaanxi",
  青海: "Qinhai",
  香港: "Hong Kong",
  黑龙江: "Heilongjiang",
};

function getCountryChineseName(s?: string) {
  if (!s) return "-";
  for (const i of Object.values(Countries)) {
    if (i.en == s) return i.cn;
  }
  if (s == "Dem. Rep. Korea") return "朝鲜";
  if (s == "Korea") return "韩国";
  if (s == "S. Sudan") return "南苏丹";
  if (s == "Central African Rep.") return "中非";
  if (s == "Dem. Rep. Congo") return "刚果（金）";
  if (s == "Congo") return "刚果（布）";
  return s;
}

export {
  ChinaProvinceSortEnName, ChinaProvinceSortName, Countries,
  CountryOption, getCountryChineseName
};

