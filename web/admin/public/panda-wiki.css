.panda-wiki-container {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 100;
  width: 68px;
  height: 68px;
  transition: width 0.5s ease-in-out;
  /* padding: 4px; */
  border-radius: 34px;
  display: none;
  user-select: none;
  cursor: pointer;
  box-shadow: 0px 10px 20px 0px rgba(13, 14, 94, 0.15);
  background: linear-gradient(90deg, rgba(61, 103, 249, 1) 0%, rgba(99, 61, 249, 0.2) 100%);
}

.panda-wiki-container::before {
  content: '';
  position: absolute;
  top: 1px;
  left: 1px;
  width: 66px;
  height: 66px;
  transition: width 0.5s ease-in-out;
  background: #fff;
  border-radius: 33px;
  z-index: -1;
}

.panda-wiki-container:hover {
  width: 250px;
  transition: width 0.5s ease-in-out;
}

.panda-wiki-container:hover .panda-wiki-hide-btn {
  display: flex;
}

.panda-wiki-container:hover::before,
.panda-wiki-container:hover .panda-wiki-widget {
  width: 248px;
  transition: width 0.5s ease-in-out;
}

.panda-wiki-hide-btn {
  display: none;
  position: absolute;
  right: 0px;
  top: 0px;
  height: 24px;
  width: 24px;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 50%;
  border: 1px solid #ECEEF1;
}

.panda-wiki-hide-btn:hover {
  border: 1px solid rgba(33, 34, 45, 0.5);
}

.panda-wiki-widget {
  overflow: hidden;
  position: absolute;
  top: 1px;
  left: 1px;
  width: 66px;
  height: 66px;
  transition: all 0.5s ease-in-out;
}

.panda-wiki-container:hover .panda-wiki-text {
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}

.panda-wiki-text {
  position: absolute;
  transition: opacity 0.5s ease-in-out;
  opacity: 1;
  font-weight: bold;
  bottom: -4px;
  z-index: 1;
  left: 0;
  color: #fff;
  font-size: 12px;
  text-align: center;
  background: linear-gradient(180deg, #3D67F9 0%, #633DF9 100%);
  border-radius: 12px;
  border: 1px solid #FFFFFF;
  width: 68px;
  height: 24px;
  line-height: 24px;
  cursor: pointer;
}

.panda-wiki-search {
  position: absolute;
  font-size: 16px;
  left: 82px;
  top: 50%;
  width: 180px;
  transform: translateY(-50%);
  color: rgba(33, 34, 45, 0.5);
}

.panda-wiki-icon {
  width: 60px;
  height: 60px;
  position: relative;
  border-radius: 50%;
  top: 4px;
  left: 4px;
  cursor: grab;
  background: linear-gradient(143deg, #3D67F9 0%, #633DF9 100%);
  box-shadow: 0px 4px 8px 0px rgba(61, 64, 249, 0.3), inset -2px -3px 4px 0px rgba(255, 255, 255, 0.4);
}

.panda-wiki-container:active .panda-wiki-icon {
  cursor: grabbing;
}

.panda-wiki-icon-panda,
.panda-wiki-icon-taiji {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 40px;
  height: 40px;
}

.panda-wiki-icon-panda {
  animation: panda-wiki-scale 2s linear infinite;
}

.panda-wiki-icon-taiji {
  animation: panda-wiki-rotate 2s linear infinite;
}

@keyframes panda-wiki-scale {
  0% {
    transform: scale(0);
  }

  50% {
    transform: scale(1);
  }

  51% {
    transform: scale(1);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes panda-wiki-rotate {
  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(360deg);
  }

  51% {
    transform: rotate(360deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.panda-wiki-modal,
.panda-wiki-hide-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9998;
  display: none;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
}

.panda-wiki-modal.active,
.panda-wiki-hide-modal.active {
  display: flex;
  justify-content: center;
  align-items: center;
}

.panda-wiki-iframe-container,
.panda-wiki-hide-container {
  position: relative;
  width: 1000px;
  max-width: calc(100% - 64px);
  height: calc(100vh - 140px);
  background: transparent;
  border-radius: 8px;
  box-shadow: 0px 10px 20px 0px rgba(54, 59, 76, 0.2);
}

.panda-wiki-hide-container {
  background-color: #fff;
  width: 500px;
  height: 189px;
  padding: 24px;
  border-radius: 10px;
}

.panda-wiki-hide-header {
  font-size: 16px;
  font-weight: bold;
  color: #21222D;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.panda-wiki-hide-body {
  font-size: 14px;
  line-height: 24px;
  color: #21222D;
  margin-bottom: 16px;
}

.panda-wiki-hide-option {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  cursor: pointer;
}

.panda-wiki-hide-option input[type="radio"] {
  appearance: none;
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border: 1px solid #999;
  border-radius: 50%;
  outline: none;
  cursor: pointer;
}

.panda-wiki-hide-option input[type="radio"]:checked {
  background-color: #3248F2;
  border-color: #3248F2;
  box-shadow: inset 0 0 0 3px white;
}

.panda-wiki-hide-body span {
  font-size: 12px;
  line-height: 24px;
  padding-left: 16px;
  color: rgba(33, 34, 45, 0.5);
}

.panda-wiki-hide-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 16px;
}

.panda-wiki-hide-footer button {
  height: 36px;
  font-size: 14px;
  font-weight: bold;
  border-radius: 10px;
  padding: 0 16px;
  cursor: pointer;
  border: none;
  transition: all 0.5s ease-in-out;
  outline: none;
}

.panda-wiki-hide-cancel-btn {
  color: #21222D;
  background: transparent;
}

.panda-wiki-hide-confirm-btn {
  color: #FFFFFF;
  background: #21222D;
}

.panda-wiki-iframe {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 8px;
}

.panda-wiki-modal-close,
.panda-wiki-hide-modal-icon {
  position: absolute;
  top: -18px;
  right: -18px;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.5s ease-in-out;
  background: #F8F9FA;
}

.panda-wiki-hide-modal-icon {
  top: 16px;
  right: 16px;
  background: #fff;
}

.panda-wiki-modal-close:hover,
.panda-wiki-hide-modal-icon:hover {
  box-shadow: 0px 10px 20px 0px rgba(54, 59, 76, 0.2);
  transform: scale(1.1);
}