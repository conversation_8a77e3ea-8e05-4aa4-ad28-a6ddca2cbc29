{"name": "panda-wiki-admin", "private": true, "version": "2.11.1", "type": "module", "scripts": {"dev": "vite", "build:dev": "vite build --m development", "build": "vite build", "icon": "node ./scripts/downLoadIcon.cjs", "api": "cx-swagger-api"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.2.0", "@mui/lab": "6.0.0-beta.19", "@mui/material": "^6.2.0", "@reduxjs/toolkit": "^2.5.0", "@tiptap/core": "^2.12.0", "@tiptap/extension-code-block-lowlight": "^2.12.0", "@tiptap/extension-color": "^2.12.0", "@tiptap/extension-highlight": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-subscript": "^2.12.0", "@tiptap/extension-superscript": "^2.12.0", "@tiptap/extension-table": "^2.12.0", "@tiptap/extension-table-cell": "^2.12.0", "@tiptap/extension-table-header": "^2.12.0", "@tiptap/extension-table-row": "^2.12.0", "@tiptap/extension-task-item": "^2.12.0", "@tiptap/extension-task-list": "^2.12.0", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/extension-text-style": "^2.12.0", "@tiptap/extension-typography": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "axios": "^1.7.9", "ct-mui": "1.0.1-beta.11", "ct-tiptap-editor": "0.7.18", "dayjs": "^1.11.13", "dnd-kit-sortable-tree": "^0.1.73", "echarts": "^5.6.0", "emoji-mart": "^5.6.0", "highlight.js": "^11.11.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "katex": "^0.16.22", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "lowlight": "^3.3.0", "prosemirror-state": "^1.4.3", "react": "^19.0.0", "react-colorful": "^5.6.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.1", "react-image-crop": "^11.0.10", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.0.2", "react-syntax-highlighter": "^15.6.1", "react-virtuoso": "^4.12.6", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "tiptap-markdown": "^0.8.10"}, "devDependencies": {"@c-x/cx-swagger-api": "^1.0.0", "@eslint/js": "^9.15.0", "@types/lodash": "^4.17.13", "@types/node": "^22.10.2", "@types/react": "^19.0.6", "@types/react-dom": "^19.0.3", "@types/react-syntax-highlighter": "^15.5.13", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.1"}, "packageManager": "pnpm@10.12.1+sha512.f0dda8580f0ee9481c5c79a1d927b9164f2c478e90992ad268bbb2465a736984391d6333d2c327913578b2804af33474ca554ba29c04a8b13060a717675ae3ac"}