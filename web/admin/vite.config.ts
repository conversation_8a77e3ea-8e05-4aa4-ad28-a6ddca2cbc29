import react from '@vitejs/plugin-react';
import path from 'path';
import { defineConfig } from 'vite';

export default defineConfig({
  server: {
    hmr: true,
    proxy: {
      '/api': {
        // target: 'http://**********:8000',
        // target: 'https://***********:2443',
        target: "http://localhost:8000",
        secure: false,
        changeOrigin: true,
      },
      '/share': {
        // target: 'http://**********:8000',
        // target: 'https://***********:2443',
        target: "http://localhost:8000",
        secure: false,
        changeOrigin: true,
      },
      '/static-file': {
        target: 'http://localhost:8000',
        secure: false,
        changeOrigin: true,
      },
    },
    host: '0.0.0.0',
  },
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
});
