import { cookies, headers } from 'next/headers';
import { getSelectorsByUserAgent } from 'react-device-detect';
import { AppRouterCacheProvider } from '@mui/material-nextjs/v13-appRouter';
import { ThemeProvider } from 'ct-mui';
import { Box } from '@mui/material';
import parse from 'html-react-parser';
import localFont from 'next/font/local';
import { darkTheme, lightTheme } from '@/theme';
import StoreProvider from '@/provider';
import { apiClient } from '@/api';
import { cache } from 'react';

const gilory = localFont({
  variable: '--font-gilory',
  src: [
    {
      path: '../../../assets/fonts/gilroy-bold-700.otf',
      weight: '700',
    },
    {
      path: '../../../assets/fonts/gilroy-medium-500.otf',
      weight: '400',
    },
    {
      path: '../../../assets/fonts/gilroy-regular-400.otf',
      weight: '300',
    },
  ],
});

const getKBDetailCached = cache(async (kb_id: string) => {
  console.log(`🔍 获取知识库详情: KB_ID=${kb_id}`);
  const result = await apiClient.serverGetKBInfo(kb_id);
  console.log(`📊 API响应:`, { success: result.success, status: result.status, message: result.message });
  if (!result.success) {
    console.log(`❌ 获取知识库详情失败: KB_ID=${kb_id}`);
    return undefined;
  }
  console.log(`✅ 获取知识库详情成功: KB_ID=${kb_id}, Name=${result.data?.name}`);
  return result.data;
})

const getNodeListCached = cache(async (kb_id: string, authToken: string) => {
  const result = await apiClient.serverGetNodeList(kb_id, authToken);
  if (!result.success) {
    return undefined;
  }
  return result.data;
})



const options = {
  replace: (domNode: any) => {
    if (domNode.attribs && domNode.name === 'script') {
      return <script {...domNode.attribs} dangerouslySetInnerHTML={{ __html: domNode.children[0]?.data || '' }} />;
    }
  },
};

const Layout = async ({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ kb_id: string }>;
}>) => {
  const headersList = await headers()
  const cookieStore = await cookies()
  const userAgent = headersList.get('user-agent');

  const { kb_id } = await params;
  console.log(`📋 Layout组件: 接收到知识库ID=${kb_id}`);
  const authToken = cookieStore.get(`auth_${kb_id}`)?.value || '';

  const kbDetail = await getKBDetailCached(kb_id)
  console.log(`📋 Layout组件: 知识库详情获取结果=${kbDetail ? '成功' : '失败'}`);
  if (kbDetail) {
    console.log(`📋 Layout组件: 知识库名称=${kbDetail.name}`);
  }
  const nodeList = await getNodeListCached(kb_id, authToken)

  const themeMode = kbDetail?.settings?.theme_mode || 'light'

  const { isMobile } = getSelectorsByUserAgent(userAgent || '');

  if (!kbDetail) {
    return <html lang="en">
      <body className={`${gilory.variable}`}>
        <Box sx={{ p: 4, textAlign: 'center' }}>
          <h1>知识库不存在</h1>
          <p>知识库 ID: {kb_id} 不存在或已被删除</p>
        </Box>
      </body>
    </html>
  }

  return <html lang="en">
    <head>
      {kbDetail?.settings?.head_code && (
        <>{parse(kbDetail.settings.head_code, options)}</>
      )}
    </head>
    <body className={`${gilory.variable}`}>
      <ThemeProvider theme={themeMode === 'dark' ? darkTheme : lightTheme}>
        <AppRouterCacheProvider>
          <StoreProvider
            kb_id={kb_id}
            kbDetail={kbDetail}
            themeMode={themeMode || 'light'}
            nodeList={nodeList || []}
            mobile={isMobile}
            token={authToken}
          >
            <Box sx={{ bgcolor: 'background.paper' }}>
              {children}
            </Box>
          </StoreProvider>
        </AppRouterCacheProvider>
      </ThemeProvider>
      {kbDetail?.settings?.body_code && (
        <>{parse(kbDetail.settings.body_code, options)}</>
      )}
    </body>
  </html>

};

export default Layout;
