@import './markdown.css';

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

body {
  font-family: var(--font-gilory), 'Roboto', 'Helvetica',
    'Arial', sans-serif !important;
}

a {
  color: inherit;
  text-decoration: none;
}

@keyframes loadingRotate {
  from {
    transform: rotate(0);
  }

  to {
    transform: rotate(360deg);
  }
}

[class^="ellipsis-"] {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ellipsis-1 {
  -webkit-line-clamp: 1;
}

.ellipsis-2 {
  -webkit-line-clamp: 2;
}

.ellipsis-3 {
  -webkit-line-clamp: 3;
}

.ellipsis-4 {
  -webkit-line-clamp: 4;
}