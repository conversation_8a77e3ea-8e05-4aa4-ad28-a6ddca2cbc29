import { useStore } from "@/provider";
import { Box, Stack } from "@mui/material";
import { usePathname, useRouter } from "next/navigation";

const QuestionList = () => {
  const { kbDetail, themeMode = 'light', mobile = false, kb_id } = useStore()
  const router = useRouter();
  const pathname = usePathname();

  if (!kbDetail?.settings?.recommend_questions) return null

  // 获取正确的聊天页面链接
  const getChatUrl = () => {
    if (pathname.startsWith('/kb/') && kb_id) {
      return `/kb/${kb_id}/chat`;
    }
    return '/chat';
  };

  const handleQuestionClick = (question: string) => {
    sessionStorage.setItem('chat_search_query', question);
    router.push(getChatUrl());
  };

  return <Stack
    direction="row"
    alignItems={'center'}
    justifyContent={'center'}
    flexWrap="wrap"
    gap={2}
    sx={{
      mt: 3,
      px: 10,
      ...(mobile && {
        px: 0,
      }),
    }}
  >
    {kbDetail?.settings?.recommend_questions?.map((item) => (
      <Box
        key={item}
        onClick={() => handleQuestionClick(item)}
        sx={{
          border: '1px solid',
          borderRadius: '16px',
          fontSize: 14,
          color: 'text.secondary',
          lineHeight: '32px',
          height: '32px',
          borderColor: 'divider',
          px: 2,
          cursor: 'pointer',
          bgcolor: themeMode === 'dark' ? 'background.paper' : 'background.default',
          '&:hover': {
            borderColor: 'primary.main',
            color: 'primary.main',
          }
        }}
      >
        {item}
      </Box>
    ))}
  </Stack>
}

export default QuestionList