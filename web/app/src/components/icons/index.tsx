import SvgIcon, { SvgIconProps } from '@mui/material/SvgIcon';

export const IconSearch = (props: SvgIconProps) => {
  return <SvgIcon
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M917.89187562 852.32177469L760.94573844 695.47157c45.5681025-60.91735781 72.71709844-136.22464313 72.7170975-218.24722687 0-201.93864281-163.8533025-365.60008031-365.88787875-365.60008032C265.83631438 111.72019625 102.07894437 275.38163281 102.07894437 477.32027563S265.83631438 842.92035594 467.87089063 842.92035594c91.71180188 0 175.26931125-33.96022781 239.44838624-89.60127938l154.73968219 154.73968219c7.67462812 7.67462812 17.74757625 11.60787469 27.91645782 11.60787469 10.16888156 0 20.14589812-3.93324656 27.91645874-11.60787469 15.34925531-15.44518875 15.34925531-40.38772875 1e-8-55.73698406z m-450.020985-88.16228625c-158.28919781 0-287.12701125-128.6459475-287.12701125-286.93514532s128.83781344-286.83921281 287.12701125-286.83921281S754.99790188 319.12701125 754.99790188 477.32027563s-128.83781344 286.83921281-287.12701125 286.83921281z"></path>
  </SvgIcon>
};

IconSearch.displayName = 'icon-sousuo';

export const IconArrowDown = (props: SvgIconProps) => {
  return <SvgIcon
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M267.337143 396.726857a38.546286 38.546286 0 0 1 51.712-2.486857l2.779428 2.486857 190.683429 190.683429 189.44-191.926857a38.546286 38.546286 0 0 1 51.785143-2.852572l2.779428 2.486857c14.116571 13.897143 15.36 36.352 2.852572 51.785143l-2.486857 2.706286L540.16 669.257143a38.546286 38.546286 0 0 1-52.077714 2.56l-2.633143-2.413714L267.337143 451.291429a38.546286 38.546286 0 0 1 0-54.564572z"></path>
  </SvgIcon>
}

IconArrowDown.displayName = 'icon-arrow-down';

export const IconArrowUp = (props: SvgIconProps) => {
  return <SvgIcon
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M566.0804999 291.39999989v581.69999971a54.3999999 54.3999999 0 1 1-108.7999998 0V291.39999989a10.90000019 10.90000019 0 0 0-18.60000029-7.5999999L203.8805 518.5999998a54.3999999 54.3999999 0 0 1-77.0000001-77.0000001L434.88050029 134.00000009a108.7999998 108.7999998 0 0 1 153.79999981 0l307.8 307.8a54.3999999 54.3999999 0 0 1-77.0000001 76.8999999L584.6805002 283.79999999a10.90000019 10.90000019 0 0 0-18.6000003 7.7000001z"></path>
  </SvgIcon>
};

IconArrowUp.displayName = 'icon-shangjiantou';

export const IconFolder = (props: SvgIconProps) => {
  return <SvgIcon
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M0 107.339673C0 48.057157 48.057157 0 107.339673 0h348.842667a53.647296 53.647296 0 0 1 41.21761 19.320755l118.075572 141.685534h337.14717c59.276075 0 107.333233 48.057157 107.333233 107.339673v699.37912a51.522013 51.522013 0 0 1-51.522013 51.522012H47.123321c-28.91673 0-47.091119-26.12166-47.123321-53.209358V107.339673z" fill="#FFA86A"></path>
    <path d="M51.522013 349.460931h956.911899a51.522013 51.522013 0 0 1 51.522013 51.522012v566.742139a51.522013 51.522013 0 0 1-51.522013 51.522012H51.522013a51.522013 51.522013 0 0 1-51.522013-51.522012v-566.742139a51.522013 51.522013 0 0 1 51.522013-51.522012z" fill="#FFD977"></path>
  </SvgIcon>
};

IconFolder.displayName = 'icon-folder';

export const IconFolderExpand = (props: SvgIconProps) => {
  return <SvgIcon
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M0 107.339673C0 48.057157 48.057157 0 107.339673 0h348.842667a53.647296 53.647296 0 0 1 41.21761 19.320755l118.075572 141.685534h337.14717c59.276075 0 107.333233 48.057157 107.333233 107.339673v397.266919L326.494994 966.037736l-220.771824 12.989987A53.666616 53.666616 0 0 1 0 966.037736V107.339673z" fill="#FFA86A" p-id="3745"></path>
    <path d="M269.608252 477.866667H1213.858616a53.666616 53.666616 0 0 1 41.47522 19.655647c10.188478 12.429686 17.813736 27.809006 11.122315 44.53434L1072.301887 976.554667a53.666616 53.666616 0 0 1-52.597535 43.149685h-966.037736a53.660176 53.660176 0 0 1-52.166037-66.225107l215.941635-434.504453c11.025711-23.770969 27.371069-41.101686 52.166038-41.108125z" fill="#FFD977" p-id="3746"></path>
  </SvgIcon>
};

IconFolder.displayName = 'icon-folder';

export const IconFile = (props: SvgIconProps) => {
  return <SvgIcon
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M929.391304 927.538087c0 51.222261-41.527652 92.749913-92.755478 92.749913H187.364174C136.136348 1020.288 94.608696 978.760348 94.608696 927.538087v-834.782609C94.608696 41.527652 136.136348 0 187.364174 0h440.709565a55.652174 55.652174 0 0 1 39.357218 16.300522L913.085217 261.954783A55.652174 55.652174 0 0 1 929.391304 301.312v626.226087z" fill="#1E89FF" p-id="3903"></path>
    <path d="M268.521739 301.45113h301.451131c23.184696 0 34.782609 11.592348 34.782608 34.782609 0 23.184696-11.597913 34.782609-34.782608 34.782609H268.521739c-23.190261 0-34.782609-11.597913-34.782609-34.782609 0-23.190261 11.592348-34.782609 34.782609-34.782609zM268.521739 510.146783h440.581565c23.184696 0 34.782609 11.592348 34.782609 34.782608 0 23.184696-11.597913 34.782609-34.782609 34.782609H268.521739c-23.190261 0-34.782609-11.597913-34.782609-34.782609 0-23.190261 11.592348-34.782609 34.782609-34.782608zM268.521739 718.842435h440.581565c23.184696 0 34.782609 11.592348 34.782609 34.782608 0 23.184696-11.597913 34.782609-34.782609 34.782609H268.521739c-23.190261 0-34.782609-11.597913-34.782609-34.782609 0-23.190261 11.592348-34.782609 34.782609-34.782608z" fill="#FFFFFF" fillOpacity=".881" p-id="3904"></path>
  </SvgIcon>
};

IconFolder.displayName = 'icon-folder';

export const IconNav = (props: SvgIconProps) => {
  return <SvgIcon
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M915 556H334.782c-60 0-60-90 0-90H915c60 0 60 90 0 90z m-0.377 371H334.405c-60 0-60-90 0-90h580.218c60 0 60 90 0 90z m0-741H334.405c-60 0-60-90 0-90h580.218c60 0 60 90 0 90zM128 206c-35.346 0-64-28.654-64-64 0-35.346 28.654-64 64-64 35.346 0 64 28.654 64 64 0 35.346-28.654 64-64 64z m0 741c-35.346 0-64-28.654-64-64 0-35.346 28.654-64 64-64 35.346 0 64 28.654 64 64 0 35.346-28.654 64-64 64z m0-371c-35.346 0-64-28.654-64-64 0-35.346 28.654-64 64-64 35.346 0 64 28.654 64 64 0 35.346-28.654 64-64 64z"></path>
  </SvgIcon>
};

IconNav.displayName = 'icon-daohangfenlei';

export const IconMore = (props: SvgIconProps) => {
  return <SvgIcon
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M66.488889 211.781818h891.022222c28.198788 0 50.980202-22.238384 50.980202-49.648485 0-27.397172-22.768485-49.648485-50.980202-49.648485H66.488889C38.341818 112.484848 15.508687 134.723232 15.508687 162.133333s22.833131 49.648485 50.980202 49.648485z m891.009293 248.242424H66.488889C38.277172 460.024242 15.508687 482.262626 15.508687 509.672727s22.768485 49.648485 50.980202 49.648485h891.022222c28.198788 0 50.980202-22.238384 50.980202-49.648485-0.012929-27.410101-22.923636-49.648485-50.993131-49.648485z m0 351.63798H66.488889c-28.134141 0-50.980202 22.238384-50.980202 49.648485s22.833131 49.648485 50.980202 49.648485h891.022222c28.198788 0 50.980202-22.238384 50.980202-49.648485-0.012929-27.397172-22.781414-49.648485-50.993131-49.648485z m0 0"></path>
  </SvgIcon>
};

IconMore.displayName = 'icon-more';

export const IconClose = (props: SvgIconProps) => {
  return <SvgIcon
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M583.125118 510.018369l423.729537-422.410436a51.062005 51.062005 0 0 0-72.08253-72.720805l-423.772089 422.580642-420.155198-422.367884a51.062005 51.062005 0 1 0-72.33784 72.33784l419.942439 422.282781-423.431676 422.240229a51.317315 51.317315 0 0 0 0 72.33784 51.062005 51.062005 0 0 0 72.33784 0l423.601883-422.325332 423.899744 426.197534a51.062005 51.062005 0 0 0 72.337841 0 51.359867 51.359867 0 0 0 0-72.33784l-423.814641-426.197534m0 0z"></path>
  </SvgIcon>
}

IconClose.displayName = 'icon-close';

export const IconClock = (props: SvgIconProps) => {
  return <SvgIcon
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M512 93.12A418.88 418.88 0 1 0 930.88 512 419.328 419.328 0 0 0 512 93.12z m0 753.92a335.104 335.104 0 1 1 335.104-335.104A335.488 335.488 0 0 1 512 847.04z"></path>
    <path d="M512 575.936a44.8 44.8 0 0 1-44.8-44.8V327.104a44.8 44.8 0 0 1 44.8-44.8 44.8 44.8 0 0 1 44.8 44.8V486.4h96a44.8 44.8 0 0 1 44.8 44.8 44.8 44.8 0 0 1-44.8 44.8z"></path>
  </SvgIcon>
}

IconClock.displayName = 'icon-clock';

export const IconLock = (props: SvgIconProps) => {
  return <SvgIcon
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M126.94374973 779.37500028L126.94374973 237.12500001A122.79374973 122.79374973 0 0 1 249.56875027 114.50000028L1095.625 114.50000028C1163.23749973 114.50000028 1218.24999972 169.51250028 1218.24999972 237.12500001l0 542.25000027C1218.24999972 846.93124974 1163.23749973 902.00000001 1095.625 902.00000001l-846.00000028 0c-67.66875 0-122.68125-55.0125-122.68125-122.62499973zM1111.20625027 237.12500001a15.63749973 15.63749973 0 0 0-15.63750055-15.58125027l-845.99999945 0c-8.60625 0-15.58125027 7.03124973-15.58125027 15.63750054l0 542.13749973a15.63749973 15.63749973 0 0 0 15.58125027 15.63749973L1095.625 794.95624974a15.63749973 15.63749973 0 0 0 15.63749972-15.63749973L1111.26249972 237.18125028z"></path>
    <path d="M672.56875 508.19375028m0-84.375a84.375 84.375 0 1 0 0 168.75 84.375 84.375 0 1 0 0-168.75Z"></path>
    <path d="M447.56874973 508.19375028m0-84.375a84.375 84.375 0 1 0 0 168.75 84.375 84.375 0 1 0 0-168.75Z"></path>
    <path d="M897.56875027 508.19375028m0-84.375a84.375 84.375 0 1 0 0 168.75 84.375 84.375 0 1 0 0-168.75Z"></path>
  </SvgIcon>
}

IconLock.displayName = 'icon-lock';

export const IconFold = (props: SvgIconProps) => {
  return <SvgIcon
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M170.666667 832a32 32 0 0 1-4.35200001-63.701333L170.666667 767.99999999 853.333333 767.99999999a32 32 0 0 1 4.352 63.70133301L853.333333 832l-682.666666 0zM170.666667 640a32 32 0 0 1-4.352-63.701333L170.666667 576.00000001l341.333333 0a32 32 0 0 1 4.352 63.70133299L512 640l-341.333333 0z m0-192.00000001a32 32 0 0 1-4.352-63.70133299L170.666667 384l341.333333 0a32 32 0 0 1 4.352 63.701333L512 447.99999999l-341.333333 0z m0-191.99999998a32 32 0 0 1-4.352-63.70133301L170.666667 192 853.333333 192a32 32 0 0 1 4.35200001 63.701333L853.333333 256.00000001l-682.666666 0zM885.333333 618.666667l0-213.333334a32 32 0 0 0-48.938666-27.136l-170.666667 106.666667a32 32 0 0 0 0 54.272l170.666667 106.666667A32 32 0 0 0 885.333333 618.666667z m-64-57.728L743.04000001 512 821.333333 463.061333l0 97.877334z"></path>
  </SvgIcon>
}

IconFold.displayName = 'icon-fold';

export const IconUnfold = (props: SvgIconProps) => {
  return <SvgIcon
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M853.333333 192a32 32 0 0 1 4.352 63.701333L853.333333 256H170.666667a32 32 0 0 1-4.352-63.701333L170.666667 192h682.666666zM853.333333 384a32 32 0 0 1 4.352 63.701333L853.333333 448h-341.333333a32 32 0 0 1-4.352-63.701333L512 384h341.333333z m0 192a32 32 0 0 1 4.352 63.701333L853.333333 640h-341.333333a32 32 0 0 1-4.352-63.701333L512 576h341.333333z m0 192a32 32 0 0 1 4.352 63.701333L853.333333 832H170.666667a32 32 0 0 1-4.352-63.701333L170.666667 768h682.666666zM138.666667 405.333333v213.333334a32 32 0 0 0 48.938666 27.136l170.666667-106.666667a32 32 0 0 0 0-54.272l-170.666667-106.666667A32 32 0 0 0 138.666667 405.333333z m64 57.728L280.96 512 202.666667 560.938667v-97.877334z"></path>
  </SvgIcon>
}

IconUnfold.displayName = 'icon-unfold';

export const IconService = (props: SvgIconProps) => {
  return <SvgIcon
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M918.089143 388.242286C908.53181 172.617143 729.86819 0 512 0S115.46819 172.860952 105.910857 388.486095A147.260952 147.260952 0 0 0 24.380952 519.850667v123.757714a147.260952 147.260952 0 0 0 147.456 146.968381h34.230858c18.334476 0 33.694476-15.11619 32.914285-33.743238V406.332952a33.889524 33.889524 0 0 0-33.694476-33.694476h-31.207619a339.675429 339.675429 0 0 1 337.67619-305.737143 339.675429 339.675429 0 0 1 337.676191 305.737143h-31.207619a33.889524 33.889524 0 0 0-33.743238 33.694476v350.500572c0 18.383238 15.11619 33.743238 33.743238 33.743238h10.825143a156.623238 156.623238 0 0 1-41.301334 74.947048c-39.253333 39.984762-101.424762 60.952381-184.661333 62.415238a33.889524 33.889524 0 0 0-32.670476-24.137143H455.094857a33.938286 33.938286 0 0 0-34.230857 34.230857v48.761905c0 19.163429 15.11619 34.230857 34.230857 34.230857h114.736762c16.091429 0 29.403429-10.825143 33.206857-25.892572 103.375238-1.511619 181.881905-29.93981 234.252191-83.529142 44.27581-46.08 57.10019-98.889143 61.147428-128.585143A147.260952 147.260952 0 0 0 999.619048 643.364571v-123.806476a146.968381 146.968381 0 0 0-81.529905-131.315809z"></path>
  </SvgIcon>
}

IconService.displayName = 'icon-service';

export const IconLogo = (props: SvgIconProps) => {
  return <SvgIcon
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M686.762667 53.248c75.404629 0 136.533333 61.128704 136.533333 136.533333 0 20.309333-4.434603 39.581013-12.387669 56.904363 22.669995 23.491925 42.857813 50.091349 59.904 79.615317 19.894272 34.459648 34.198869 70.638251 43.193685 107.47904 25.127595 3.267243 41.510229 7.581696 43.95008 8.246614l0.318123 0.086016-26.547542 201.842688 0.354987 0.049152c29.353301 4.401835 46.280704 43.387563 37.875712 87.711744-7.546197 39.795371-32.9728 70.501717-59.531264 74.209962l-10.985472 83.511979c-121.413632-33.161216-229.553493-25.206784-324.420949 23.861931l0.771413-0.395947v0.270336c-4.392277 5.165056-11.143851 8.669867-20.25472 10.513067l-0.786432 0.152917c-9.510912 1.798144-17.722027 1.798144-24.638805 0l-67.433814-320.019115c-15.777792-9.530027-176.00512-102.225237-326.568618-30.49472l106.243413 300.630016s211.049131-71.625387 287.759019 49.883819c-104.383829-28.654251-213.265067-13.751637-326.639616 44.705109l-36.950016-104.38656c-26.819243 0.512-56.754176-25.843029-70.437547-63.98225-13.808981-38.487381-6.376107-75.897515 16.207872-89.221803L50.890069 537.490773s21.482155-11.295403 55.990955-22.227626a412.987392 412.987392 0 0 1 39.692971-160.585046C121.81504 329.966933 106.496 295.796736 106.496 258.048c0-75.404629 61.128704-136.533333 136.533333-136.533333 37.699584 0 71.830187 15.279445 96.538624 39.983786 72.698539-34.802347 151.475541-46.216533 227.098624-36.716544C589.772117 82.176683 634.888192 53.248 686.762667 53.248z m196.614826 440.933035c-150.250837-35.999744-278.988117 69.640192-305.143808 92.976469l-2.441216 324.098731c50.581504-130.699264 266.719232-105.132032 266.719232-105.132032z m-547.480917-275.237547c-106.490539 61.482325-169.757355 169.418752-179.011584 283.557888 86.852949-16.874155 210.567168-13.144064 306.0736 100.914517l-0.002731 0.016384c25.137152 10.971819 47.523157 14.793387 67.155286 11.461974 17.794389-3.017387 32.586411-11.126101 44.377429-24.322048-0.730453 0.682667-1.100459 1.04448-1.100459 1.04448 72.226133-137.592832 200.2944-162.740907 288.748886-161.559894-7.918933-27.090944-19.163819-53.671253-33.890304-79.17841-99.390805-172.149419-320.229376-231.309312-492.350123-131.934891z m26.342741 100.562261c-13.008896 28.736171-17.978709 48.709632-22.562133 66.8672l-0.375467 1.488214c-0.314027 1.236992-0.625323 2.467157-0.939349 3.691861l-0.436907 1.69984c-0.147456 0.565248-0.293547 1.129131-0.442368 1.693013l-0.445098 1.690283c-1.873237 7.035563-3.915776 13.991936-6.608214 21.307392-17.683797 48.055637-87.044096 42.726741-87.044096-11.401899s33.445205-91.481429 57.940651-109.808298c24.495445-18.326869 81.575936-22.867968 60.912981 22.77376z m157.021526 43.492694c28.59008 13.873152-3.072 57.498283-32.471723 61.629781-29.401088 4.131499-72.193365-33.30048-50.330283-49.993045 21.863083-16.693931 54.211925-25.511253 82.802006-11.636736z m95.054506-126.936406c30.377301 3.623595 78.016512 19.248469 105.081515 66.125824 27.063637 46.877355-30.339072 86.171648-69.681152 53.398187-6.948181-5.789013-12.896939-11.595776-18.837504-17.637376l-1.2288-1.253376c-0.707243-0.722261-1.414485-1.447253-2.124459-2.177707l-1.06769-1.099093c-13.194581-13.57824-27.522389-28.545024-53.508779-47.179093-40.71424-29.193557 10.989568-53.80096 41.366869-50.177366z"></path>
  </SvgIcon>
}

IconLogo.displayName = 'icon-logo';

export const IconZan = (props: SvgIconProps) => {
  return <SvgIcon
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M569.9584 0c57.1392 0 89.2928 27.3408 105.8816 49.9712 49.9712 70.2464 27.3408 191.488-2.3552 284.3648h234.496c35.6352 0 68.9152 15.4624 91.5456 42.9056 20.1728 24.9856 28.5696 57.0368 22.528 88.064L954.368 794.7264c-23.7568 105.8816-96.256 176.128-182.0672 176.128h-624.64C66.56 970.9568 0 905.4208 0 823.5008v-6.0416l52.3264-396.288a90.3168 90.3168 0 0 1 90.4192-85.6064h133.12c5.12-11.8784 11.0592-23.4496 18.0224-34.5088L437.8624 78.5408C462.848 40.448 495.0016 0 569.9584 0z m0 95.232c-24.9856 0-33.28 4.7104-52.3264 35.6352L373.6576 353.3824c-14.336 22.528-21.504 47.616-21.504 72.6016v35.84l-0.7168 189.8496-0.1024 57.344-0.2048 82.7392v53.248l-0.1024 30.72H772.096c39.3216 0 76.1856-41.5744 89.2928-101.0688l66.56-329.728c0-1.024 1.2288-4.7104-2.3552-8.192-3.4816-4.8128-10.6496-7.168-17.8176-7.168H663.9616a91.8528 91.8528 0 0 1-78.5408-44.032 83.5584 83.5584 0 0 1-3.584-78.5408c29.696-92.7744 36.864-173.7728 16.6912-201.1136C596.1728 102.4 590.2336 95.232 569.9584 95.232zM255.7952 429.568H147.5584v1.1264L95.232 825.7536a53.248 53.248 0 0 0 52.224 49.9712h108.3392V429.568z"></path>
  </SvgIcon>
}

IconZan.displayName = 'icon-zan';

export const IconZaned = (props: SvgIconProps) => {
  return <SvgIcon
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M255.8976 335.4624v635.392l-108.3392 0.1024C66.56 970.9568 0 905.4208 0 823.5008v-6.0416l52.3264-396.288a90.3168 90.3168 0 0 1 90.4192-85.6064L256 335.4624zM569.9584 0c57.1392 0 89.2928 27.3408 105.8816 49.9712 49.9712 70.2464 27.3408 191.488-2.3552 284.3648h234.496c35.6352 0 68.9152 15.4624 91.5456 42.9056 20.1728 24.9856 28.5696 57.0368 22.528 88.064L954.368 794.7264c-23.7568 105.8816-96.256 176.128-182.0672 176.128L351.232 970.8544V212.48L437.8624 78.5408C462.848 40.448 495.0016 0 569.9584 0z"></path>
  </SvgIcon>
}

IconZaned.displayName = 'icon-zaned';

export const IconCai = (props: SvgIconProps) => {
  return <SvgIcon
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M454.0416 970.9568c-57.1392 0-89.2928-27.3408-105.8816-49.9712-49.9712-70.2464-27.3408-191.488 2.3552-284.3648h-234.496c-35.6352 0-68.9152-15.4624-91.5456-42.9056a107.7248 107.7248 0 0 1-22.528-88.064L69.632 176.128C93.4912 70.2464 165.888 0 251.8016 0h624.64C957.44 0 1024 65.536 1024 147.456V153.6l-52.3264 396.288a90.3168 90.3168 0 0 1-90.4192 85.6064h-133.12c-5.12 11.8784-11.0592 23.4496-18.0224 34.5088L586.1376 892.416c-24.9856 38.0928-57.1392 78.5408-132.096 78.5408z m0-95.232c24.9856 0 33.28-4.7104 52.3264-35.6352L650.3424 617.472c14.336-22.6304 21.504-47.616 21.504-72.6016v-9.5232-26.3168l0.7168-189.8496 0.1024-57.344 0.2048-82.7392v-53.248l0.1024-30.72H251.904c-39.3216 0-76.1856 41.5744-89.2928 101.0688l-66.56 329.728c0 1.024-1.2288 4.7104 2.3552 8.192 3.4816 4.8128 10.6496 7.168 17.8176 7.168h243.9168c32.1536 0 61.8496 16.6912 78.5408 44.032 14.336 23.7568 15.4624 53.5552 3.584 78.5408-29.696 92.7744-36.864 173.7728-16.6912 201.1136 2.3552 3.584 8.2944 10.6496 28.5696 10.6496z m314.1632-334.336h108.2368v-1.1264l52.3264-395.0592A53.248 53.248 0 0 0 876.544 95.232H768.1024v446.1568z"></path>
  </SvgIcon>
}

IconCai.displayName = 'icon-cai';

export const IconCaied = (props: SvgIconProps) => {
  return <SvgIcon
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M768.1024 635.4944V0.1024L876.4416 0C957.44 0 1024 65.536 1024 147.456V153.6l-52.3264 396.288a90.3168 90.3168 0 0 1-90.4192 85.6064L768 635.4944zM454.0416 970.9568c-57.1392 0-89.2928-27.3408-105.8816-49.9712-49.9712-70.2464-27.3408-191.488 2.3552-284.3648h-234.496c-35.6352 0-68.9152-15.4624-91.5456-42.9056a107.7248 107.7248 0 0 1-22.528-88.064L69.632 176.128C93.4912 70.2464 165.888 0 251.8016 0L672.768 0.1024v758.3744l-86.7328 133.9392c-24.9856 38.0928-57.1392 78.5408-132.096 78.5408z"></path>
  </SvgIcon>
}

IconCaied.displayName = 'icon-caied';