import { parsePathname } from '@/utils';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { apiClient } from '../api';

const StatPage = {
  welcome: 1,
  node: 2,
  chat: 3,
  auth: 4
}

export async function middleware(request: NextRequest, headers: Record<string, string>, session: string) {
  const url = request.nextUrl.clone()
  const { page, id } = parsePathname(url.pathname)
  console.log(`🍊 >>> ${url.pathname}`)

  // 支持通过URL路径访问知识库: /kb/{kb_id}/...
  let kb_id = request.headers.get('x-kb-id') || process.env.DEV_KB_ID || '';
  let kbPathMatch: RegExpMatchArray | null = null;

  // 检查是否是 /kb/{kb_id} 格式的路径
  kbPathMatch = url.pathname.match(/^\/kb\/([^\/]+)(.*)$/);
  if (kbPathMatch) {
    kb_id = kbPathMatch[1]; // 提取知识库ID
    console.log(`🔄 KB路径访问: ${url.pathname}, KB_ID: ${kb_id}`);
    // 对于 /kb/{kb_id} 路径，不需要重写URL，让Next.js的动态路由处理
  }

  const authToken = request.cookies.get(`auth_${kb_id}`)?.value || '';

  try {
    // 获取节点列表
    const nodeListResult = await apiClient.serverGetNodeList(kb_id, authToken);
    if (nodeListResult.status === 401 && !url.pathname.startsWith('/auth')) {
      const loginUrl = new URL('/auth/login', request.url)
      loginUrl.searchParams.set('redirect', url.pathname)
      return NextResponse.redirect(loginUrl)
    }
    if (url.pathname === '/') {
      return NextResponse.redirect(new URL('/welcome', request.url))
    }

    // 页面上报
    const pages = Object.keys(StatPage)
    if (pages.includes(page) || pages.includes(id)) {
      apiClient.serviceStatPage({
        kb_id,
        authToken,
        scene: StatPage[page as keyof typeof StatPage],
        node_id: id || '',
        headers,
        session
      })
    }

    // 如果是KB路径访问，设置知识库ID到请求头
    if (kbPathMatch) {
      const response = NextResponse.next();
      response.headers.set('x-kb-id', kb_id);
      return response;
    }

    return NextResponse.next()
  } catch (error) {
    console.log(error)
  }

  // 如果是KB路径访问，设置知识库ID到请求头
  if (kbPathMatch) {
    const response = NextResponse.next();
    response.headers.set('x-kb-id', kb_id);
    return response;
  }

  return NextResponse.next()
}