import { NextRequest, NextResponse } from "next/server";

export async function middleware(request: NextRequest) {
  const url = request.nextUrl.clone();

  // 支持通过URL路径访问知识库: /kb/{kb_id}/...
  let kb_id = request.headers.get('x-kb-id') || process.env.DEV_KB_ID || '';

  // 首先尝试从当前URL路径中提取知识库ID
  const currentPath = url.pathname;
  console.log(`🔍 Client API: 当前路径=${currentPath}`);

  // 检查referer是否包含知识库ID信息
  const referer = request.headers.get('referer') || '';
  console.log(`🔍 Client API: referer=${referer}`);

  // 更精确的正则表达式，匹配UUID格式的知识库ID
  const kbPathMatch = referer.match(/\/kb\/([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})/i);
  if (kbPathMatch) {
    kb_id = kbPathMatch[1]; // 从referer中提取知识库ID
    console.log(`🔄 Client API: 从referer提取KB_ID: ${kb_id}`);
  } else {
    console.log(`❌ Client API: 无法从referer提取KB_ID, 当前KB_ID: ${kb_id}`);
  }

  const authToken = request.cookies.get(`auth_${kb_id}`)?.value || '';
  console.log('🍎 client api >>>', url.pathname, ' >>> KB_ID:', kb_id)

  // 修改请求头，添加 KB_ID，然后让 Next.js rewrites 处理代理
  const requestHeaders = new Headers(request.headers);
  requestHeaders.set('x-kb-id', kb_id);
  requestHeaders.set('X-Simple-Auth-Password', authToken);

  // 重写 URL 路径从 /client/ 到 /share/
  url.pathname = url.pathname.replace(/^\/client\//, '/share/');

  console.log('🔄 Client API: 重写路径为:', url.pathname);

  // 创建新的请求，让 Next.js rewrites 处理
  return NextResponse.rewrite(url, {
    request: {
      headers: requestHeaders,
    },
  });

}