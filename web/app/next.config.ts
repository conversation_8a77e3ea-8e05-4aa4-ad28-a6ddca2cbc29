import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  distDir: 'dist',
  reactStrictMode: false,
  allowedDevOrigins: ['10.10.18.71'],
  output: 'standalone',
  logging: {
    fetches: {
      fullUrl: true,
    },
  },
  async rewrites() {
    const rewritesPath = [];

    // 生产环境和开发环境都需要 API 代理
    rewritesPath.push(
      ...[
        {
          source: '/static-file/:path*',
          destination: 'http://panda-wiki-api:8000/static-file/:path*',
          basePath: false as const,
        },
        {
          source: '/share/v1/:path*',
          destination: 'http://panda-wiki-api:8000/share/v1/:path*',
          basePath: false as const,
        },
        // 将 /client/v1/ 重写为 /share/v1/，因为后端只有 share 路由
        {
          source: '/client/v1/:path*',
          destination: 'http://172.19.0.6:8000/share/v1/:path*',
          basePath: false as const,
        },
      ]
    );

    return rewritesPath;
  },
};

export default nextConfig;
