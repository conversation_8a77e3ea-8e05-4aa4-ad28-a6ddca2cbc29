# PandaWiki 一键部署

🚀 **在任何有Docker环境的Mac或Linux电脑上，只需一条命令即可完成PandaWiki的完整部署！**

## 快速开始

### 系统要求
- Docker 20.10+
- Docker Compose 1.29+ 或 Docker Compose V2
- 至少 4GB 可用内存
- 至少 10GB 可用磁盘空间

### 一键部署

```bash
# 1. 克隆项目到本地
git clone <repository-url>
cd baidadoc-wiki

# 2. 一键部署（这是唯一需要的命令）
c
```

### 如果遇到镜像拉取超时

如果在中国大陆网络环境下遇到镜像拉取超时，请先配置Docker镜像加速器：

```bash
# 配置Docker镜像加速器（仅需运行一次）
./setup-docker-mirror.sh

# 然后重新部署
docker-compose -f docker-compose.prod.yml up -d
```

就这么简单！🎉

## 访问地址

部署完成后（通常需要5-15分钟），可以通过以下地址访问：

| 服务 | 地址 | 说明 |
|------|------|------|
| **用户前台** | http://localhost:3010 | 主要的知识库界面 |
| **管理后台** | http://localhost:5173 | 系统管理界面 |
| **API接口** | http://localhost:8000 | RESTful API |
| **MinIO控制台** | http://localhost:9001 | 对象存储管理 |

## 默认登录信息

### 管理后台
- **用户名**: `admin`
- **密码**: `admin123`

### MinIO控制台
- **用户名**: `pandawiki`
- **密码**: `eMu4Gf9S1GJr5AZb6GiAuf12k064ei6OfnWTFytPXDM=`

> ⚠️ **安全提示**: 生产环境请立即修改默认密码！

## 常用管理命令

```bash
# 查看服务状态
docker-compose -f docker-compose.prod.yml ps

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f

# 重启服务
docker-compose -f docker-compose.prod.yml restart

# 停止服务
docker-compose -f docker-compose.prod.yml down

# 停止并删除所有数据（危险操作）
docker-compose -f docker-compose.prod.yml down -v
```

## 故障排除

### 1. 镜像拉取超时
如果遇到镜像拉取超时错误：
```bash
# 先配置Docker镜像加速器
./setup-docker-mirror.sh

# 然后重新部署
docker-compose -f docker-compose.prod.yml up -d
```

其他解决方案：
- 检查网络连接
- 等待几分钟后重试
- 使用VPN或代理

### 2. 端口冲突
确保以下端口未被占用：
- 5432 (PostgreSQL)
- 6379 (Redis)
- 4222 (NATS)
- 9000, 9001 (MinIO)
- 8000 (API)
- 5173 (管理后台)
- 3010 (用户前台)

### 3. 内存不足
```bash
# 清理Docker缓存
docker system prune -a
```

### 4. 服务启动失败
```bash
# 查看详细错误日志
docker-compose -f docker-compose.prod.yml logs [service-name]
```

## 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Admin Panel   │    │   User App      │    │   API Server    │
│   (Port 5173)   │    │   (Port 3010)   │    │   (Port 8000)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │     Redis       │    │     MinIO       │
│   (Port 5432)   │    │   (Port 6379)   │    │   (Port 9000)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                 │
                       ┌─────────────────┐
                       │      NATS       │
                       │   (Port 4222)   │
                       └─────────────────┘
```

## 优化特性

- ✅ 支持Docker镜像加速器配置，解决网络问题
- ✅ 使用国内npm和Go模块代理，加速构建
- ✅ 自动构建所有必需的Docker镜像
- ✅ 包含健康检查和自动重启
- ✅ 数据持久化存储
- ✅ 完整的服务编排

---

**🎉 享受使用 PandaWiki！**
