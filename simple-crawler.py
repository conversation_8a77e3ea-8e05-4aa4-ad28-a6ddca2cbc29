#!/usr/bin/env python3
"""
Simple crawler service for PandaWiki development
Provides basic URL scraping functionality
"""
import json
import requests
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib.parse
from bs4 import BeautifulSoup
import re

class CrawlerHandler(BaseHTTPRequestHandler):
    def do_POST(self):
        if self.path == '/api/v1/scrape':
            try:
                # 读取请求体
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                request_data = json.loads(post_data.decode('utf-8'))
                
                url = request_data.get('url', '')
                kb_id = request_data.get('kb_id', '')

                if not url:
                    self.send_error_response(400, "URL is required")
                    return

                # 检查是否是本地文件路径
                if url.startswith('/static-file/') or url.startswith('static-file/'):
                    self.send_error_response(400, "Cannot scrape local files. Please provide an external URL (e.g., https://example.com)")
                    return

                # 确保URL有协议前缀
                if not url.startswith(('http://', 'https://')):
                    url = 'https://' + url
                
                # 抓取URL内容
                scraped_data = self.scrape_url(url)
                
                # 返回成功响应 (匹配PandaWiki期望的格式)
                response = {
                    "err": 0,
                    "msg": "",
                    "data": {
                        "title": scraped_data.get("title", ""),
                        "markdown": scraped_data.get("content", "")
                    }
                }
                
                self.send_json_response(200, response)
                
            except Exception as e:
                print(f"Error processing scrape request: {e}")
                self.send_error_response(500, f"Scraping failed: {str(e)}")
        else:
            self.send_error_response(404, "Not Found")
    
    def scrape_url(self, url):
        """抓取URL内容"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 提取标题
            title = ""
            if soup.title:
                title = soup.title.string.strip() if soup.title.string else ""
            
            # 提取描述
            description = ""
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            if meta_desc:
                description = meta_desc.get('content', '')
            
            # 提取关键词
            keywords = []
            meta_keywords = soup.find('meta', attrs={'name': 'keywords'})
            if meta_keywords:
                keywords = [k.strip() for k in meta_keywords.get('content', '').split(',')]
            
            # 提取主要内容
            content = ""
            
            # 尝试多种方式提取内容
            content_selectors = [
                'article',
                '.content',
                '.post-content',
                '.entry-content',
                'main',
                '.main-content',
                '#content',
                '.article-body'
            ]
            
            for selector in content_selectors:
                content_elem = soup.select_one(selector)
                if content_elem:
                    content = content_elem.get_text(strip=True)
                    break
            
            # 如果没有找到特定的内容区域，提取body中的文本
            if not content:
                # 移除脚本和样式标签
                for script in soup(["script", "style", "nav", "header", "footer"]):
                    script.decompose()
                
                content = soup.get_text()
                # 清理多余的空白字符
                content = re.sub(r'\s+', ' ', content).strip()
            
            # 限制内容长度
            if len(content) > 10000:
                content = content[:10000] + "..."
            
            return {
                "title": title,
                "content": content,
                "description": description,
                "keywords": keywords,
                "author": "",
                "publish_date": ""
            }
            
        except requests.RequestException as e:
            error_msg = str(e)
            if "Max retries exceeded" in error_msg:
                raise Exception(f"Unable to connect to the website. Please check if the URL is correct and accessible.")
            elif "SSL" in error_msg:
                raise Exception(f"SSL connection failed. The website may have certificate issues or be unreachable.")
            elif "Connection refused" in error_msg:
                raise Exception(f"Connection refused. The website may be down or blocking requests.")
            else:
                raise Exception(f"Failed to fetch URL: {error_msg}")
        except Exception as e:
            raise Exception(f"Failed to parse content: {str(e)}")
    
    def send_json_response(self, status_code, data):
        self.send_response(status_code)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'POST, GET, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))
    
    def send_error_response(self, status_code, message):
        response = {
            "err": 1,
            "msg": message,
            "data": None
        }
        self.send_json_response(status_code, response)
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'POST, GET, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        # 自定义日志格式
        print(f"🕷️  Crawler: {format % args}")

def start_crawler_service():
    server = HTTPServer(('localhost', 8080), CrawlerHandler)
    print("🕷️  Simple Crawler Service started on http://localhost:8080")
    print("📡 Available endpoints:")
    print("   POST /api/v1/scrape - Scrape URL content")
    print("🔄 Ready to process scraping requests...")
    server.serve_forever()

if __name__ == '__main__':
    try:
        # 检查依赖
        import requests
        from bs4 import BeautifulSoup
        start_crawler_service()
    except ImportError as e:
        print(f"❌ Missing dependencies: {e}")
        print("📦 Please install required packages:")
        print("   pip install requests beautifulsoup4")
