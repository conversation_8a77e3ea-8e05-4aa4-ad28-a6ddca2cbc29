#!/bin/bash

# PandaWiki 一键部署脚本
# 适用于快速部署和测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🐼 PandaWiki 一键部署脚本${NC}"
echo "=================================="

# 检查 Docker
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker 未安装，请先安装 Docker${NC}"
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo -e "${RED}❌ Docker Compose 未安装，请先安装 Docker Compose${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker 环境检查通过${NC}"

# 创建数据目录
echo -e "${BLUE}📁 创建数据目录...${NC}"
mkdir -p data/{postgres,redis,minio,nats}

# 生成随机密码
DB_PASSWORD=$(openssl rand -base64 16 | tr -d "=+/" | cut -c1-16)
S3_SECRET=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-32)
JWT_SECRET=$(openssl rand -base64 64 | tr -d "=+/" | cut -c1-64)

echo -e "${BLUE}🔧 生成配置文件...${NC}"

# 创建环境变量文件
cat > .env << EOF
# 自动生成的配置文件
POSTGRES_PASSWORD=$DB_PASSWORD
S3_SECRET_KEY=$S3_SECRET
JWT_SECRET=$JWT_SECRET
ADMIN_PASSWORD=admin123

# 端口配置
API_PORT=8000
ADMIN_PORT=5173
APP_PORT=3010
POSTGRES_PORT=5432
REDIS_PORT=6379
NATS_PORT=4222
MINIO_PORT=9000
MINIO_CONSOLE_PORT=9001
EOF

# 创建简化的 docker-compose 文件
cat > docker-compose.quick.yml << 'EOF'
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: pandawiki-postgres
    environment:
      POSTGRES_DB: panda-wiki
      POSTGRES_USER: panda-wiki
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: pandawiki-redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - ./data/redis:/data
    restart: unless-stopped

  nats:
    image: nats:2.10-alpine
    container_name: pandawiki-nats
    ports:
      - "${NATS_PORT:-4222}:4222"
    command: ["--jetstream", "--store_dir", "/data"]
    volumes:
      - ./data/nats:/data
    restart: unless-stopped

  minio:
    image: minio/minio:latest
    container_name: pandawiki-minio
    environment:
      MINIO_ROOT_USER: s3panda-wiki
      MINIO_ROOT_PASSWORD: ${S3_SECRET_KEY}
    ports:
      - "${MINIO_PORT:-9000}:9000"
      - "${MINIO_CONSOLE_PORT:-9001}:9001"
    volumes:
      - ./data/minio:/data
    command: server /data --console-address ":9001"
    restart: unless-stopped

  # 如果有预构建的镜像，使用以下配置
  # api:
  #   image: pandawiki/api:latest
  #   container_name: pandawiki-api
  #   environment:
  #     - PG_DSN=host=postgres user=panda-wiki password=${POSTGRES_PASSWORD} dbname=panda-wiki port=5432 sslmode=disable TimeZone=Asia/Shanghai
  #     - REDIS_ADDR=redis:6379
  #     - MQ_NATS_SERVER=nats://nats:4222
  #     - S3_ENDPOINT=minio:9000
  #     - S3_ACCESS_KEY=s3panda-wiki
  #     - S3_SECRET_KEY=${S3_SECRET_KEY}
  #     - JWT_SECRET=${JWT_SECRET}
  #     - ADMIN_PASSWORD=${ADMIN_PASSWORD}
  #     - CADDY_API=disabled
  #     - MAX_KB=50
  #     - CRAWLER_SERVICE_URL=disabled
  #   ports:
  #     - "${API_PORT:-8000}:8000"
  #   depends_on:
  #     - postgres
  #     - redis
  #     - minio
  #     - nats
  #   restart: unless-stopped

  # admin:
  #   image: pandawiki/admin:latest
  #   container_name: pandawiki-admin
  #   ports:
  #     - "${ADMIN_PORT:-5173}:80"
  #   depends_on:
  #     - api
  #   restart: unless-stopped

  # app:
  #   image: pandawiki/app:latest
  #   container_name: pandawiki-app
  #   ports:
  #     - "${APP_PORT:-3010}:3010"
  #   depends_on:
  #     - api
  #   restart: unless-stopped
EOF

echo -e "${BLUE}🚀 启动基础服务...${NC}"
docker-compose -f docker-compose.quick.yml --env-file .env up -d postgres redis nats minio

echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
sleep 10

echo -e "${GREEN}✅ 基础服务部署完成！${NC}"
echo
echo -e "${BLUE}📋 服务信息：${NC}"
echo -e "PostgreSQL: ${GREEN}localhost:${POSTGRES_PORT:-5432}${NC}"
echo -e "Redis: ${GREEN}localhost:${REDIS_PORT:-6379}${NC}"
echo -e "NATS: ${GREEN}localhost:${NATS_PORT:-4222}${NC}"
echo -e "MinIO: ${GREEN}localhost:${MINIO_PORT:-9000}${NC}"
echo -e "MinIO Console: ${GREEN}http://localhost:${MINIO_CONSOLE_PORT:-9001}${NC}"
echo
echo -e "${BLUE}🔑 登录信息：${NC}"
echo -e "数据库密码: ${YELLOW}$DB_PASSWORD${NC}"
echo -e "MinIO 用户名: ${YELLOW}s3panda-wiki${NC}"
echo -e "MinIO 密码: ${YELLOW}$S3_SECRET${NC}"
echo -e "管理员密码: ${YELLOW}admin123${NC}"
echo
echo -e "${BLUE}📝 配置文件已保存到 .env${NC}"
echo
echo -e "${YELLOW}💡 提示：${NC}"
echo "1. 基础服务已启动，现在可以手动启动 PandaWiki API 和前端"
echo "2. 或者构建 Docker 镜像后使用完整的 docker-compose 部署"
echo "3. 使用 'docker-compose -f docker-compose.quick.yml logs' 查看日志"
echo "4. 使用 'docker-compose -f docker-compose.quick.yml down' 停止服务"
